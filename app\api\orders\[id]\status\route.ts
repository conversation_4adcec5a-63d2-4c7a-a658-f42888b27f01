import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";
import prisma from "@/app/utils/db";

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    const { status, note, invoiceAM } = await request.json();
    
    // Validate status
    const validStatuses = ["plasata", "completa", "anulata"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: "Invalid status" },
        { status: 400 }
      );
    }
    
    // Get user email for audit
    const user = await prisma.user.findFirst({
      where: { externalId: userId },
      select: { email: true }
    });
    
    const updatedBy = user?.email || userId;
    
    // Update order in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Update order status
      const updatedOrder = await tx.order.update({
        where: { id: params.id },
        data: { 
          orderStatus: status,
          updatedBy,
          
          invoiceAM: invoiceAM || null
        }
      });
      
      // Create status history entry
      await tx.orderStatusHistory.create({
        data: {
          orderId: params.id,
          notes: note,
          changedBy: updatedBy
        }
      });
      
      return updatedOrder;
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error updating order status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}