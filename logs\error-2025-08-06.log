{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-08-06 11:29:06"}
{"level":"error","message":"Clerk API error: [Error]\nMessage:Unprocessable Entity\nStatus:422\nSerialized errors: {\"code\":\"form_password_length_too_short\",\"message\":\"Passwords must be 12 characters or more.\",\"longMessage\":\"Passwords must be 12 characters or more.\",\"meta\":{\"paramName\":\"password\"}},{\"code\":\"form_password_no_special_char\",\"message\":\"Passwords must contain at least one of the following special characters: !\\\"#$%&'()*+,-./:;<=>?@[]^_`{|}~.\",\"longMessage\":\"Passwords must contain at least one of the following special characters: !\\\"#$%&'()*+,-./:;<=>?@[]^_`{|}~.\",\"meta\":{\"paramName\":\"password\"}}\nClerk Trace ID: c61932e4b20ca6145cdf11edb174641a for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 15:06:38"}
{"level":"error","message":"Clerk API error: [Error]\nMessage:Unprocessable Entity\nStatus:422\nSerialized errors: {\"code\":\"form_password_pwned\",\"message\":\"Password has been found in an online data breach. For account safety, please use a different password.\",\"longMessage\":\"Password has been found in an online data breach. For account safety, please use a different password.\",\"meta\":{\"paramName\":\"password\"}}\nClerk Trace ID: 211003cc214bdd6cc8025c2b7788ce46 for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 15:13:38"}
{"level":"error","message":"[updateUser]Failed to update email in Clerk: [Error]\nMessage:Bad Request\nStatus:400\nSerialized errors: {\"code\":\"last_required_identification_deletion_failed\",\"message\":\"Deleting your last email address is prohibited\",\"longMessage\":\"You are required to maintain at least one email address in your account at all times\",\"meta\":{}}\nClerk Trace ID: 01cf73888586a94a76145dd70923826e for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 16:12:48"}
{"level":"error","message":"[updateUser]Failed to update email in Clerk: [Error]\nMessage:Bad Request\nStatus:400\nSerialized errors: {\"code\":\"last_required_identification_deletion_failed\",\"message\":\"Deleting your last email address is prohibited\",\"longMessage\":\"You are required to maintain at least one email address in your account at all times\",\"meta\":{}}\nClerk Trace ID: 88e719fcc2578dc4762eb39a2556a794 for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 16:12:59"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-06 16:43:28"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-06 16:44:10"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-06 16:44:11"}
