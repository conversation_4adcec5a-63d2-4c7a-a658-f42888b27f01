import { notFound } from "next/navigation";
import Link from "next/link";
import { getOrderById } from "@/app/getData/order/getOrders";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatCurrency, formatDate, formatOrderStatus } from "@/app/utils/formatters";
import OrderStatusUpdate from "@/app/components/order/OrderStatusUpdate";
import OrderItemsTable from "@/app/components/order/OrderItemsTable";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnStatus } from "@/generated/prisma";
import { getReturnsByOrderId } from "@/app/getData/return/data";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import BillingAddress from "@/app/components/order/BillingAddress";
import ShippingAddress from "@/app/components/order/ShippingAddress";

export default async function OrderDetailPage({ params }: { params: Promise<{ id: string }> }) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const order = await getOrderById(paramsObject.id);
  const returns = await getReturnsByOrderId(paramsObject.id);
  
  const products = order?.orderItems.map( (o) => ({
    id: o.id,
    name: o.product?.Description_Local,
    code: o.product?.Material_Number,
    quantity: o.quantity,
    price: o.price.toNumber(),
    notes: o.notes,
    notesToInvoice: o.notesToInvoice,
    vinOrderItem: o.vinOrderItem,
  }));

  if (!order) {
    return notFound();
  }

  // Add this helper function
  const getStatusColor = (status: ReturnStatus) => {
    switch (status) {
      case ReturnStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ReturnStatus.approved:
        return "bg-green-100 text-green-800";
      case ReturnStatus.received:
        return "bg-purple-100 text-purple-800";
      case ReturnStatus.inspected:
        return "bg-indigo-100 text-indigo-800";
      case ReturnStatus.awaitingReceipt:
        return "bg-yellow-100 text-yellow-800";
        case ReturnStatus.refundIssued:
        return "bg-gray-100 text-gray-800";
        case ReturnStatus.completed:
        return "bg-emerald-100 text-emerald-800";
      case ReturnStatus.rejected:
        return "bg-red-100 text-red-800";
      case ReturnStatus.cancelled:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <Link href="/orders">
              <Button variant="outline" size="sm" className="mb-2">
                ← Back to Orders
              </Button>
            </Link>
            <h1 className="text-3xl font-bold">
              Order {order.orderNumber}
              <Badge className="ml-2" variant={order.isPaid ? "default" : "secondary"}>
                {order.isPaid ? "Paid" : "Pending Payment"}
              </Badge>
            </h1>
            <p className="text-muted-foreground">
              Placed on {formatDate(order.createdAt)}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">Print Invoice</Button>
            {order.orderStatus !== "anulata" && (
              <Button variant="destructive">Cancel Order</Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">

        <Card>
          <CardHeader>
            <CardTitle>Sumar Comandă</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Order Status */}
            <div>
              <p className="text-sm font-medium text-muted-foreground mb-1">Status</p>
              <Badge variant="outline" className="mt-4 text-base">
                {formatOrderStatus(order.orderStatus)}
              </Badge>
            </div>

            {/* Financial Breakdown */}
            <div className="space-y-2">
              <div className="flex justify-between items-center text-muted-foreground">
                <span>Subtotal</span>
                <span>
                  {formatCurrency(order.amount.toNumber())}
                </span>
              </div>

              <div className="flex justify-between items-center text-muted-foreground">
                <span>Taxă livrare</span>
                <span>
                  {formatCurrency(order.shippingCost ? order.shippingCost.toNumber() : 0)}
                </span>
              </div>

              {/* Divider */}
              <div className="border-t border-dashed my-4"></div>

              {/* Grand Total */}
              <div className="flex justify-between items-center text-lg font-bold">
                <span>Total</span>
                <span>
                  {formatCurrency(order.totalAmount.toNumber())}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
          <Card>
            <CardHeader>
              <CardTitle>Customer</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <h3 className="font-semibold">
                  {order.user?.firstName} {order.user?.lastName}
                </h3>
                <p>{order.user?.email}</p>
              </div>
              <div className="flex justify-end">
                <Link href={`/users/${order.userId}`}>
                  <Button variant="outline" size="sm">View Customer</Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Payment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Method:</span>
                  <span className="font-medium">{order.paymentMethod}</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <Badge variant={order.isPaid ? "default" : "secondary"}>
                    {order.isPaid ? "Paid" : "Pending"}
                  </Badge>
                </div>
                {order.invoiceAM && (
                  <div className="flex justify-between">
                    <span>Invoice:</span>
                    <span className="font-medium">{order.invoiceAM}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Update</CardTitle>
            </CardHeader>
            <CardContent>
              <OrderStatusUpdate orderId={order.id} invoiceAMprop={order.invoiceAM || ""} currentStatus={order.orderStatus} />
            </CardContent>
          </Card>
        </div>

        {/* Returns Section */}
        <div className="mt-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Returns</h2>
            <Button asChild>
              <Link href={`/orders/${order.id}/return`}>Create Return</Link>
            </Button>
          </div>
          
          {returns.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-6 text-center">
              <p className="text-gray-500">No returns found for this order.</p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Return #</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {returns.map((returnItem) => (
                    <TableRow key={returnItem.id}>
                      <TableCell className="font-medium">
                        {returnItem.returnNumber}
                      </TableCell>
                      <TableCell>{formatDate(returnItem.createdAt)}</TableCell>
                      <TableCell>{returnItem.returnItems.length} items</TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(returnItem.status)}>
                          {returnItem.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button asChild variant="outline" size="sm">
                          <Link href={`/returns/${returnItem.id}`}>
                            View Details
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>

        <Tabs defaultValue="items" className="mb-6">
          <TabsList>
            <TabsTrigger value="items">Order Items</TabsTrigger>
            <TabsTrigger value="addresses">Addresses</TabsTrigger>
            <TabsTrigger value="history">Status History</TabsTrigger>
            {order.notes && <TabsTrigger value="notes">Notes</TabsTrigger>}
          </TabsList>
          
          <TabsContent value="items">
            <Card>
              <CardHeader>
                <CardTitle>Order Items</CardTitle>
              </CardHeader>
              <CardContent>
                <OrderItemsTable items={products} />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="addresses">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Billing Address</CardTitle>
                </CardHeader>
                <CardContent>
                  <BillingAddress billingAddress={order.billingAddress} />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Shipping Address: </CardTitle>
                </CardHeader>
                <CardContent>
                  <ShippingAddress
                    order={order}
                    shippingAddress={order.shippingAddress}
                  />  
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>Status History</CardTitle>
              </CardHeader>
              <CardContent>
                {order.statusHistory.length === 0 ? (
                  <p className="text-muted-foreground">No status history available</p>
                ) : (
                  <div className="space-y-4">
                    {order.statusHistory.map((status) => (
                      <div key={status.id} className="flex justify-between border-b pb-2">
                        <div>
                          <Badge variant="outline">{formatOrderStatus(status.orderStatus?.toString() || "")}</Badge>
                          {status.notes && (
                            <p className="text-sm text-muted-foreground mt-1">{status.notes}</p>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatDate(status.createdAt)}
                          {status.changedBy && (
                            <div>by {status.changedBy}</div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          {order.notes && (
            <TabsContent value="notes">
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="whitespace-pre-wrap">{order.notes}</p>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
  );
}


