-- Remove the legacy 1-column unique on returnId (if present)
ALTER TABLE "PostPurchaseAction"
  DROP CONSTRAINT IF EXISTS "PostPurchaseAction_returnId_key";
DROP INDEX IF EXISTS "PostPurchaseAction_returnId_key";

-- Ensure the correct composite unique exists (safe if already created)
CREATE UNIQUE INDEX IF NOT EXISTS "PostPurchaseAction_returnId_orderItemId_key"
ON "PostPurchaseAction"("returnId","orderItemId");
