"use client";

import { useState, useTransition } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { 
  CreateReturnFormValues, 
  createReturnSchema, 
  ReturnItemFormValues 
} from "@/app/zod/returnSchemas";
import { createReturn } from "@/app/actions/returnActions";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { ReturnItemReason, ReturnReason } from "@/generated/prisma";

interface OrderItem {
  id: string;
  productName: string| null;
  productCode: string;
  quantity: number;
  price: number;
}

interface ReturnFormProps {
  orderId: string;
  orderItems: OrderItem[];
}

export default function ReturnForm({ orderId, orderItems }: ReturnFormProps) {
  const [isPending, startTransition] = useTransition();
  const [serverError, setServerError] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<CreateReturnFormValues>({
    resolver: zodResolver(createReturnSchema),
    defaultValues: {
      orderId,
      reason: ReturnReason.other,
      items: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  const onSubmit = (data: CreateReturnFormValues) => {
    setServerError(null);
    
    startTransition(async () => {
      try {
        const response = await createReturn(data);
        
        if (response.status === "SUCCESS") {
          toast.success(response.message);
          
          // If we have the return ID, navigate to it
          if (response.data?.returnId) {
            router.push(`/returns/${response.data.returnId}`);
          } else {
            // Otherwise just refresh the order page
            router.push(`/orders/${orderId}`);
          }
        } else {
          toast.error(response.message);
          
          // Handle field errors
          if (response.fieldErrors) {
            Object.entries(response.fieldErrors).forEach(([field, message]) => {
              form.setError(field as any, { message });
            });
          } else {
            setServerError(response.message);
          }
        }
      } catch (error) {
        console.error("Error creating return:", error);
        toast.error("An unexpected error occurred");
        setServerError("An unexpected error occurred. Please try again.");
      }
    });
  };

  const addItem = () => {
    append({
      orderItemId: "",
      quantity: 1,
      reason: ReturnItemReason.other,
      description: "",
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {serverError && (
          <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {serverError}
          </div>
        )}
        
        <FormField
          control={form.control}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Return Reason</FormLabel>
              <Select 
                onValueChange={field.onChange} 
                defaultValue={field.value}
                disabled={isPending}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(ReturnReason).map((reason) => (
                    <SelectItem key={reason} value={reason}>
                      {reason.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="additionalNotes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Notes</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Please provide any additional details about your return"
                  {...field}
                  disabled={isPending}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Items to Return</h3>
            <Button 
              type="button" 
              variant="outline" 
              onClick={addItem}
              disabled={isPending}
            >
              Add Item
            </Button>
          </div>
          
          {fields.length === 0 && (
            <p className="text-sm text-gray-500">
              Please add at least one item to return
            </p>
          )}
          
          {fields.map((field, index) => (
            <div key={field.id} className="p-4 border rounded-md space-y-4">
              <div className="flex justify-between">
                <h4 className="font-medium">Return Item {index + 1}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => remove(index)}
                  disabled={isPending}
                >
                  Remove
                </Button>
              </div>
              
              <FormField
                control={form.control}
                name={`items.${index}.orderItemId`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={isPending}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a product" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {orderItems.map((item) => (
                          <SelectItem key={item.id} value={item.id}>
                            {item.productName} (Qty: {item.quantity})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name={`items.${index}.quantity`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantity to Return</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min={1} 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name={`items.${index}.reason`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={isPending}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a reason" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(ReturnItemReason).map((reason) => (
                          <SelectItem key={reason} value={reason}>
                            {reason.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name={`items.${index}.description`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Please describe the issue with this item"
                        {...field}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}
        </div>
        
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isPending || fields.length === 0}
          >
            {isPending ? "Submitting..." : "Submit Return Request"}
          </Button>
        </div>
      </form>
    </Form>
  );
}