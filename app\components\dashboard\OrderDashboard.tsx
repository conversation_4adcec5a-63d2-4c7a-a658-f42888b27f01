  import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

export default async function OrderDashboard() {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Orders</CardTitle>
                <CardDescription>
                    Manage customer orders
                </CardDescription>
            </CardHeader>
            <CardContent>
                <p className="text-sm text-gray-500">No recent orders found.</p>
            </CardContent>
            <CardFooter>
                <Button asChild className="w-full">
                    <Link href="/orders">View All Orders</Link>
                </Button>
            </CardFooter>
        </Card>
    );
}