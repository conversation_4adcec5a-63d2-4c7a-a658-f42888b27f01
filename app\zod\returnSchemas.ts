import { ReturnItemReason, ReturnReason, ReturnStatus } from "@/generated/prisma";
import { z } from "zod";

// Schema for return item
const returnItemSchema = z.object({
  orderItemId: z.string(),
  quantity: z.number().int().positive(),
  reason: z.nativeEnum(ReturnItemReason),
  description: z.string().optional(),
});

// Schema for creating a return
export const createReturnSchema = z.object({
  orderId: z.string(),
  reason: z.nativeEnum(ReturnReason),
  additionalNotes: z.string().optional(),
  items: z.array(returnItemSchema).min(1, "At least one item must be returned"),
});

// Schema for updating a return
export const updateReturnSchema = z.object({
  returnId: z.string(),
  status: z.nativeEnum(ReturnStatus).optional(),
  isApproved: z.boolean().optional(),
  approvedBy: z.string().optional(),
  rejectionReason: z.string().optional(),
  refundAmount: z.number().optional(),
  refundMethod: z.string().optional(),
  notes: z.string().optional(),
});

// Types based on schemas
export type CreateReturnFormValues = z.infer<typeof createReturnSchema>;
export type UpdateReturnFormValues = z.infer<typeof updateReturnSchema>;
export type ReturnItemFormValues = z.infer<typeof returnItemSchema>;