{"level":"info","message":"User cmcnevyak0006hx78b8yc8oz4 details retrieved for userEmail","timestamp":"2025-08-07 23:12:03"}
{"level":"info","message":"User cmcnevyak0006hx78b8yc8oz4 details retrieved for userEmail","timestamp":"2025-08-07 23:12:54"}
{"level":"info","message":"Banner updated successfully: cmbyp3o9r0000hx6smiv3<NAME_EMAIL>","timestamp":"2025-08-07 23:18:20"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-08-07 23:18:35"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-07 23:18:38"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-07 23:30:59"}
