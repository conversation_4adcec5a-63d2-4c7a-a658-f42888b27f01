import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import prisma from "@/app/utils/db";
import { ServiceStatus, ResolutionType } from "@/generated/prisma";
import { requireAdminOrModerator } from "@/lib/auth-utils";

const updateStatusSchema = z.object({
  status: z.nativeEnum(ServiceStatus),
  notes: z.string().optional(),
  resolution: z.nativeEnum(ResolutionType).optional(),
  resolutionNotes: z.string().optional(),
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and authorization
    const user = await requireAdminOrModerator();
    
    // Validate request body
    const body = await request.json();
    const validatedData = updateStatusSchema.parse(body);
    
    // Get the current service request
    const currentService = await prisma.serviceRequest.findUnique({
      where: { id: params.id },
      select: { status: true, resolution: true },
    });

    if (!currentService) {
      return NextResponse.json(
        { error: "Service request not found" },
        { status: 404 }
      );
    }

    // Update the service request
    const updatedService = await prisma.serviceRequest.update({
      where: { id: params.id },
      data: {
        status: validatedData.status,
        resolution: validatedData.resolution,
        resolutionNotes: validatedData.resolutionNotes,
        resolvedAt: validatedData.status === ServiceStatus.completed ? new Date() : undefined,
        updatedAt: new Date(),
      },
    });

    // Create status history entry
    await prisma.serviceStatusHistory.create({
      data: {
        serviceRequestId: params.id,
        previousStatus: currentService.status,
        newStatus: validatedData.status,
        notes: validatedData.notes,
        changedBy: user.id,
        changedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      service: updatedService,
    });

  } catch (error) {
    console.error("Error updating service status:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
