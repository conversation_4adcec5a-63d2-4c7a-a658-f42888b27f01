-- Create<PERSON><PERSON>
CREATE TYPE "BannerPlacement" AS ENUM ('HOME', 'CATEGORY', 'PRODUCT', 'CHECKOUT', 'SIDEBAR', 'HEADER', 'FOOTER', 'POPUP', 'HERO', 'CATEGORY_SECTION_LANDING_PAGE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "DeviceTarget" AS ENUM ('ALL', 'DESKTOP', 'MOBILE', 'TABLET');

-- CreateEnum
CREATE TYPE "ActionType" AS ENUM ('RETURN', 'SERVICE');

-- CreateEnum
CREATE TYPE "ActionStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'CANCELLED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ChangeType" AS ENUM ('CRON_DISCOUNT_EXPIRED', 'MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS', 'INSERT', 'UPDATE', 'DELETE', 'ORDER', 'DISCOUNT', 'PRODUCT_ADDED', 'PRODUCT_DELETE_ALL', 'PRODUCT_DELETE', 'PRODUCT_ADD_TO_DISCOUNT_WITH_CSV', 'ADDED_TO_DISCOUNT', 'DELETED_FROM_DISCOUNT');

-- CreateEnum
CREATE TYPE "DiscountType" AS ENUM ('PERCENTAGE', 'FIXED_AMOUNT', 'NEW_PRICE');

-- CreateEnum
CREATE TYPE "Rol" AS ENUM ('administAB', 'moderatorAB', 'inregistratAB', 'fourLvlAdminAB', 'fourLvlInregistratAB', 'angajatAB');

-- CreateEnum
CREATE TYPE "Showroom" AS ENUM ('CJ', 'BV', 'TM', 'AR', 'BAC', 'BAN', 'OTP', 'MIL', 'TGM', 'JIL', 'CT', 'CRA', 'SB');

-- CreateEnum
CREATE TYPE "OrderStatus" AS ENUM ('plasata', 'procesare', 'confirmata', 'pregatita', 'expediata', 'livrata', 'completa', 'anulata', 'stornata', 'returnata', 'partiala');

-- CreateEnum
CREATE TYPE "ShippingMethod" AS ENUM ('curier', 'intern', 'showroom');

-- CreateEnum
CREATE TYPE "ShipmentStatus" AS ENUM ('asteptare', 'prelucrare', 'pregatit', 'expediat', 'tranzit', 'livrat', 'esuat', 'intors', 'anulat', 'partial');

-- CreateEnum
CREATE TYPE "PaymentMethod" AS ENUM ('ramburs', 'card', 'transfer', 'laTermen');

-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('asteptare', 'succes', 'esuat', 'rambursat', 'partial_rambursat', 'contestat');

-- CreateEnum
CREATE TYPE "StockStatus" AS ENUM ('IN_STOCK', 'LOW_STOCK', 'OUT_OF_STOCK', 'DISCONTINUED', 'UNKNOWN');

-- CreateEnum
CREATE TYPE "IssueType" AS ENUM ('DEFECTIVE_PART', 'DAMAGED_IN_SHIPPING', 'WRONG_ITEM_RECEIVED', 'MISSING_PARTS', 'NOT_AS_DESCRIBED', 'COMPATIBILITY_ISSUE', 'OTHER');

-- CreateEnum
CREATE TYPE "ServiceStatus" AS ENUM ('requested', 'scheduled', 'inProgress', 'diagnosisComplete', 'awaitingParts', 'awaitingApproval', 'completed', 'cancelled', 'delivered');

-- CreateEnum
CREATE TYPE "ResolutionType" AS ENUM ('REPLACEMENT', 'REFUND', 'STORE_CREDIT', 'REPAIR', 'PARTIAL_REFUND', 'NO_ACTION_NEEDED');

-- CreateEnum
CREATE TYPE "ReturnStatus" AS ENUM ('requested', 'approved', 'rejected', 'awaitingReceipt', 'received', 'inspected', 'refundIssued', 'completed', 'cancelled');

-- CreateEnum
CREATE TYPE "ReturnReason" AS ENUM ('wrongItem', 'defective', 'damaged', 'notAsDescribed', 'noLongerWanted', 'other');

-- CreateEnum
CREATE TYPE "ReturnItemReason" AS ENUM ('wrongItem', 'defective', 'damaged', 'notAsDescribed', 'noLongerWanted', 'other');

-- CreateEnum
CREATE TYPE "ItemCondition" AS ENUM ('asDescribed', 'damaged', 'opened', 'used', 'missingParts');

-- CreateEnum
CREATE TYPE "InspectionResult" AS ENUM ('approved', 'rejected', 'partiallyApproved');

-- CreateEnum
CREATE TYPE "RefundMethod" AS ENUM ('originalPayment', 'storeCredit', 'bankTransfer');

-- CreateEnum
CREATE TYPE "NotificationEventType" AS ENUM ('ORDER_PLACED', 'ORDER_CONFIRMED', 'ORDER_PROCESSING', 'ORDER_SHIPPED', 'ORDER_OUT_FOR_DELIVERY', 'ORDER_DELIVERED', 'ORDER_CANCELLED', 'RETURN_REQUESTED', 'RETURN_APPROVED', 'RETURN_REJECTED', 'RETURN_SHIPPED_BACK', 'RETURN_RECEIVED', 'RETURN_REFUNDED', 'SERVICE_REQUESTED', 'SERVICE_APPROVED', 'SERVICE_SCHEDULED', 'SERVICE_IN_PROGRESS', 'SERVICE_COMPLETED', 'SERVICE_CANCELLED', 'PAYMENT_SUCCESSFUL', 'PAYMENT_FAILED', 'PAYMENT_REFUNDED', 'ACCOUNT_CREATED', 'PASSWORD_RESET', 'LOGIN_SUSPICIOUS', 'PROFILE_UPDATED', 'NEW_PRODUCT_ANNOUNCEMENT', 'PROMOTIONAL_OFFER', 'PRICE_DROP_ALERT', 'BACK_IN_STOCK', 'NEWSLETTER', 'MAINTENANCE_SCHEDULED', 'SYSTEM_UPDATE', 'FEATURE_ANNOUNCEMENT', 'CART_ABANDONED', 'REVIEW_REQUEST', 'REORDER_SUGGESTION');

-- CreateEnum
CREATE TYPE "NotificationCategory" AS ENUM ('ORDER_STATUS', 'RETURN_STATUS', 'SERVICE_STATUS', 'PAYMENT', 'SECURITY', 'PROMOTIONAL', 'SYSTEM', 'REMINDER');

-- CreateEnum
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');

-- CreateEnum
CREATE TYPE "NotificationChannel" AS ENUM ('IN_APP', 'EMAIL', 'PUSH', 'SMS');

-- CreateEnum
CREATE TYPE "DeliveryStatus" AS ENUM ('PENDING', 'SENT', 'DELIVERED', 'FAILED', 'BOUNCED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "DigestFrequency" AS ENUM ('IMMEDIATE', 'HOURLY', 'DAILY', 'WEEKLY', 'NEVER');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "emailVerified" TIMESTAMP(3),
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "profileImage" TEXT NOT NULL,
    "userAM" TEXT,
    "phoneNumber" TEXT,
    "phoneVerified" BOOLEAN DEFAULT false,
    "externalId" TEXT,
    "externalProvider" TEXT,
    "role" "Rol" NOT NULL DEFAULT 'inregistratAB',
    "lastLoginAt" TIMESTAMP(3),
    "loginCount" INTEGER NOT NULL DEFAULT 0,
    "lastActivityAt" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "inactiveBy" TEXT,
    "inactiveAt" TIMESTAMP(3),
    "inactiveReason" TEXT,
    "isSuspended" BOOLEAN NOT NULL DEFAULT false,
    "suspendedBy" TEXT,
    "suspendedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "suspensionReason" TEXT,
    "deletedAt" TIMESTAMP(3),
    "deletedBy" TEXT,
    "deletedReason" TEXT,
    "passwordEnabled" BOOLEAN NOT NULL DEFAULT false,
    "twoFactorEnabled" BOOLEAN NOT NULL DEFAULT false,
    "totpEnabled" BOOLEAN NOT NULL DEFAULT false,
    "mfaEnabledAt" TIMESTAMP(3),
    "mfaDisabledAt" TIMESTAMP(3),
    "loginAttempts" INTEGER NOT NULL DEFAULT 0,
    "lockoutUntil" TIMESTAMP(3),
    "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "pushNotifications" BOOLEAN NOT NULL DEFAULT false,
    "smsNotifications" BOOLEAN NOT NULL DEFAULT false,
    "newsletterOptIn" BOOLEAN NOT NULL DEFAULT false,
    "legal_accepted_at" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserNotificationPreference" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "eventType" "NotificationEventType" NOT NULL,
    "inApp" BOOLEAN NOT NULL DEFAULT true,
    "email" BOOLEAN NOT NULL DEFAULT true,
    "push" BOOLEAN NOT NULL DEFAULT false,
    "sms" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserNotificationPreference_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserNotificationSetting" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "category" "NotificationCategory" NOT NULL,
    "emailEnabled" BOOLEAN NOT NULL DEFAULT true,
    "pushEnabled" BOOLEAN NOT NULL DEFAULT true,
    "smsEnabled" BOOLEAN NOT NULL DEFAULT false,
    "immediateDelivery" BOOLEAN NOT NULL DEFAULT true,
    "digestFrequency" "DigestFrequency",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserNotificationSetting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserNotification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "eventType" "NotificationEventType" NOT NULL,
    "category" "NotificationCategory" NOT NULL,
    "priority" "NotificationPriority" NOT NULL DEFAULT 'NORMAL',
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "link" TEXT,
    "metaData" JSONB,
    "createdBy" TEXT,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "isArchived" BOOLEAN NOT NULL DEFAULT false,
    "archivedAt" TIMESTAMP(3),
    "actionRequired" BOOLEAN NOT NULL DEFAULT false,
    "actionTaken" BOOLEAN NOT NULL DEFAULT false,
    "actionTakenAt" TIMESTAMP(3),
    "relatedOrderId" TEXT,
    "relatedActionId" TEXT,
    "relatedProductId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserNotificationDelivery" (
    "id" TEXT NOT NULL,
    "notificationId" TEXT NOT NULL,
    "channel" "NotificationChannel" NOT NULL,
    "status" "DeliveryStatus" NOT NULL DEFAULT 'PENDING',
    "sentAt" TIMESTAMP(3),
    "failedAt" TIMESTAMP(3),
    "failureReason" TEXT,
    "externalId" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "maxRetries" INTEGER NOT NULL DEFAULT 3,
    "nextRetryAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserNotificationDelivery_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserAuditLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "action" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT,
    "details" TEXT,
    "detailsJson" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "performedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserSession" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "deviceId" TEXT,
    "location" TEXT,
    "lastActiveAt" TIMESTAMP(3),
    "isRevoked" BOOLEAN NOT NULL DEFAULT false,
    "revokedReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserGroup" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "permissions" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "UserGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "amount" DECIMAL(15,2) NOT NULL,
    "totalAmount" DECIMAL(15,2) NOT NULL,
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "vin" TEXT,
    "invoiceAM" TEXT,
    "updatesEnabled" BOOLEAN NOT NULL DEFAULT true,
    "terms" BOOLEAN NOT NULL DEFAULT true,
    "notes" TEXT,
    "shippingCost" DECIMAL(15,2),
    "orderStatus" "OrderStatus" NOT NULL DEFAULT 'plasata',
    "paymentStatus" "PaymentStatus" NOT NULL DEFAULT 'asteptare',
    "paymentMethod" "PaymentMethod" NOT NULL DEFAULT 'ramburs',
    "shippingMethod" "ShippingMethod" NOT NULL DEFAULT 'curier',
    "shipmentStatus" "ShipmentStatus" NOT NULL DEFAULT 'asteptare',
    "showroom" "Showroom",
    "placedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "cancelledAt" TIMESTAMP(3),
    "shippingProcessedAt" TIMESTAMP(3),
    "shippedAt" TIMESTAMP(3),
    "deliveredAt" TIMESTAMP(3),
    "paidAt" TIMESTAMP(3),
    "refundedAt" TIMESTAMP(3),
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),
    "archivedAt" TIMESTAMP(3),
    "groupId" TEXT,
    "billingAddressId" TEXT,
    "shippingAddressId" TEXT,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "hasReturns" BOOLEAN NOT NULL DEFAULT false,
    "hasServiceRequests" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderItem" (
    "id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "notes" TEXT,
    "notesToInvoice" BOOLEAN NOT NULL DEFAULT false,
    "vinOrderItem" TEXT,
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "orderId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrderItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderStatusHistory" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "orderStatus" "OrderStatus",
    "paymentStatus" "PaymentStatus",
    "shipmentStatus" "ShipmentStatus",
    "previousOrderStatus" "OrderStatus",
    "previousPaymentStatus" "PaymentStatus",
    "previousShipmentStatus" "ShipmentStatus",
    "reason" TEXT,
    "notes" TEXT,
    "changedBy" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "OrderStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderPostPurchaseAction" (
    "id" TEXT NOT NULL,
    "actionNumber" TEXT NOT NULL,
    "orderItemId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "ActionType" NOT NULL,
    "status" "ActionStatus" NOT NULL DEFAULT 'ACTIVE',
    "returnId" TEXT,
    "serviceRequestId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrderPostPurchaseAction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Wishlist" (
    "id" TEXT NOT NULL,
    "productCode" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Wishlist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShippingAddress" (
    "id" TEXT NOT NULL,
    "fullName" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "county" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "notes" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShippingAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BillingAddress" (
    "id" TEXT NOT NULL,
    "fullName" TEXT NOT NULL,
    "companyName" TEXT,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "county" TEXT NOT NULL,
    "cui" TEXT,
    "bank" TEXT,
    "iban" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BillingAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Banner" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "subtitle" TEXT,
    "imageUrl" TEXT NOT NULL,
    "mobileImageUrl" TEXT,
    "callToAction" TEXT,
    "buttonText" TEXT,
    "description" TEXT,
    "url" TEXT,
    "placement" "BannerPlacement" NOT NULL DEFAULT 'HOME',
    "position" INTEGER NOT NULL DEFAULT 0,
    "width" TEXT,
    "height" TEXT,
    "backgroundColor" TEXT,
    "textColor" TEXT,
    "textAlignment" TEXT,
    "targetAudience" TEXT,
    "deviceTarget" "DeviceTarget" NOT NULL DEFAULT 'ALL',
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "impressions" INTEGER NOT NULL DEFAULT 0,
    "clicks" INTEGER NOT NULL DEFAULT 0,
    "conversionRate" DOUBLE PRECISION,
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Banner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategoryLevel1" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT false,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CategoryLevel1_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategoryLevel2" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT false,
    "imageUrl" TEXT,
    "level1Id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CategoryLevel2_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategoryLevel3" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "descriere" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT false,
    "familyCode" TEXT,
    "imageUrl" TEXT,
    "slug" TEXT,
    "metaTitle" TEXT,
    "metaDescription" TEXT,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "productCount" INTEGER NOT NULL DEFAULT 0,
    "lastProductAdded" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),
    "level2Id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CategoryLevel3_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategorySection" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "image" TEXT NOT NULL,
    "href" TEXT NOT NULL,

    CONSTRAINT "CategorySection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Brand" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT true,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Brand_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VehicleModel" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "VehicleModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductClass" (
    "id" TEXT NOT NULL,
    "classCode" TEXT NOT NULL,
    "brandId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductClass_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductClassVehicleModel" (
    "productClassId" TEXT NOT NULL,
    "vehicleModelId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductClassVehicleModel_pkey" PRIMARY KEY ("productClassId","vehicleModelId")
);

-- CreateTable
CREATE TABLE "Product" (
    "id" TEXT NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "Net_Weight" TEXT,
    "Description_Local" TEXT,
    "Base_Unit_Of_Measur" TEXT,
    "Cross_Plant" TEXT,
    "New_Material" TEXT,
    "PretAM" DECIMAL(15,2),
    "FinalPrice" DECIMAL(15,2),
    "HasDiscount" BOOLEAN NOT NULL DEFAULT false,
    "activeDiscountType" "DiscountType",
    "activeDiscountValue" DECIMAL(10,2),
    "discountPercentage" DECIMAL(5,2),
    "priceRange" TEXT,
    "ImageUrl" TEXT[] DEFAULT ARRAY['https://op47vimj99.ufs.sh/f/6Hnm5nafTbm964jRPnfTbm9EeHnDOzysS6K5X27Upql8xtjN']::TEXT[],
    "IsOnLandingPage" BOOLEAN NOT NULL DEFAULT false,
    "Material_Number_normalized" TEXT,
    "Description_Local_normalized" TEXT,
    "stockStatus" "StockStatus" NOT NULL DEFAULT 'UNKNOWN',
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),
    "Parts_Class" TEXT DEFAULT 'undefined-class',
    "classId" TEXT,
    "Material_Group" TEXT DEFAULT 'undefined-category',
    "categoryLevel3Id" TEXT,
    "isServiceable" BOOLEAN NOT NULL DEFAULT false,
    "warrantyMonths" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductPriceHistory" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "oldPretAM" DECIMAL(15,2),
    "newPretAM" DECIMAL(15,2),
    "oldFinalPrice" DECIMAL(15,2),
    "newFinalPrice" DECIMAL(15,2),
    "reason" TEXT,
    "source" TEXT,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductPriceHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductHistory" (
    "id" UUID NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "snapshot" JSONB NOT NULL,
    "change_type" "ChangeType" NOT NULL,
    "version" INTEGER NOT NULL,
    "changed_by" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Discount" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" "DiscountType" NOT NULL,
    "value" DECIMAL(10,2) NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Discount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductDiscount" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductDiscount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DiscountHistory" (
    "id" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "snapshot" JSONB NOT NULL,
    "change_type" "ChangeType" NOT NULL,
    "version" INTEGER NOT NULL,
    "changed_by" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DiscountHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductAttribute" (
    "id" TEXT NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "key" TEXT,
    "value" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductAttribute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductAttributeHistory" (
    "id" TEXT NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "snapshot" JSONB NOT NULL,
    "change_type" "ChangeType" NOT NULL,
    "version" INTEGER NOT NULL,
    "changed_by" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductAttributeHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Return" (
    "id" TEXT NOT NULL,
    "returnNumber" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "status" "ReturnStatus" NOT NULL DEFAULT 'requested',
    "addressId" TEXT NOT NULL,
    "isApproved" BOOLEAN,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "refundAmount" DECIMAL(15,2),
    "refundMethod" "RefundMethod",
    "refundedAt" TIMESTAMP(3),
    "refundReference" TEXT,
    "returnShippingLabel" TEXT,
    "receivedAt" TIMESTAMP(3),
    "inspectedAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "updatedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Return_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReturnItem" (
    "id" TEXT NOT NULL,
    "returnId" TEXT NOT NULL,
    "orderItemId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "reason" "ReturnItemReason" NOT NULL,
    "condition" "ItemCondition" NOT NULL DEFAULT 'asDescribed',
    "description" TEXT,
    "isReceived" BOOLEAN NOT NULL DEFAULT false,
    "isInspected" BOOLEAN NOT NULL DEFAULT false,
    "inspectionNotes" TEXT,
    "inspectionResult" "InspectionResult",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReturnItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReturnStatusHistory" (
    "id" TEXT NOT NULL,
    "returnId" TEXT NOT NULL,
    "previousStatus" "ReturnStatus",
    "newStatus" "ReturnStatus" NOT NULL,
    "notes" TEXT,
    "changedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ReturnStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceRequest" (
    "id" TEXT NOT NULL,
    "serviceNumber" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "issueType" "IssueType" NOT NULL,
    "description" TEXT NOT NULL,
    "addressId" TEXT NOT NULL,
    "status" "ServiceStatus" NOT NULL DEFAULT 'requested',
    "resolution" "ResolutionType",
    "resolutionNotes" TEXT,
    "resolvedAt" TIMESTAMP(3),
    "lastEmailSentAt" TIMESTAMP(3),
    "emailsSent" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ServiceRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceStatusHistory" (
    "id" TEXT NOT NULL,
    "serviceRequestId" TEXT NOT NULL,
    "previousStatus" "ServiceStatus",
    "newStatus" "ServiceStatus" NOT NULL,
    "notes" TEXT,
    "changedBy" TEXT NOT NULL,
    "changedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "emailSent" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ServiceStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_UserToGroup" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_UserToGroup_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_userAM_key" ON "User"("userAM");

-- CreateIndex
CREATE UNIQUE INDEX "User_externalId_key" ON "User"("externalId");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "User_lastName_firstName_idx" ON "User"("lastName", "firstName");

-- CreateIndex
CREATE INDEX "User_phoneNumber_idx" ON "User"("phoneNumber");

-- CreateIndex
CREATE INDEX "User_lastLoginAt_idx" ON "User"("lastLoginAt");

-- CreateIndex
CREATE INDEX "User_isActive_deletedAt_idx" ON "User"("isActive", "deletedAt");

-- CreateIndex
CREATE INDEX "User_externalId_idx" ON "User"("externalId");

-- CreateIndex
CREATE INDEX "User_role_idx" ON "User"("role");

-- CreateIndex
CREATE INDEX "User_lastActivityAt_idx" ON "User"("lastActivityAt");

-- CreateIndex
CREATE INDEX "User_isSuspended_idx" ON "User"("isSuspended");

-- CreateIndex
CREATE INDEX "UserNotificationPreference_userId_idx" ON "UserNotificationPreference"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserNotificationPreference_userId_eventType_key" ON "UserNotificationPreference"("userId", "eventType");

-- CreateIndex
CREATE INDEX "UserNotificationSetting_userId_idx" ON "UserNotificationSetting"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserNotificationSetting_userId_category_key" ON "UserNotificationSetting"("userId", "category");

-- CreateIndex
CREATE INDEX "UserNotification_userId_isRead_idx" ON "UserNotification"("userId", "isRead");

-- CreateIndex
CREATE INDEX "UserNotification_userId_createdAt_idx" ON "UserNotification"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "UserNotification_userId_category_idx" ON "UserNotification"("userId", "category");

-- CreateIndex
CREATE INDEX "UserNotification_userId_priority_idx" ON "UserNotification"("userId", "priority");

-- CreateIndex
CREATE INDEX "UserNotification_eventType_idx" ON "UserNotification"("eventType");

-- CreateIndex
CREATE INDEX "UserNotification_relatedOrderId_idx" ON "UserNotification"("relatedOrderId");

-- CreateIndex
CREATE INDEX "UserNotification_relatedActionId_idx" ON "UserNotification"("relatedActionId");

-- CreateIndex
CREATE INDEX "UserNotificationDelivery_notificationId_idx" ON "UserNotificationDelivery"("notificationId");

-- CreateIndex
CREATE INDEX "UserNotificationDelivery_status_channel_idx" ON "UserNotificationDelivery"("status", "channel");

-- CreateIndex
CREATE UNIQUE INDEX "UserNotificationDelivery_notificationId_channel_key" ON "UserNotificationDelivery"("notificationId", "channel");

-- CreateIndex
CREATE INDEX "UserAuditLog_userId_idx" ON "UserAuditLog"("userId");

-- CreateIndex
CREATE INDEX "UserAuditLog_action_idx" ON "UserAuditLog"("action");

-- CreateIndex
CREATE INDEX "UserAuditLog_entityType_entityId_idx" ON "UserAuditLog"("entityType", "entityId");

-- CreateIndex
CREATE INDEX "UserAuditLog_createdAt_idx" ON "UserAuditLog"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "UserSession_sessionToken_key" ON "UserSession"("sessionToken");

-- CreateIndex
CREATE INDEX "UserSession_sessionToken_idx" ON "UserSession"("sessionToken");

-- CreateIndex
CREATE INDEX "UserSession_userId_expiresAt_idx" ON "UserSession"("userId", "expiresAt");

-- CreateIndex
CREATE INDEX "UserSession_isRevoked_idx" ON "UserSession"("isRevoked");

-- CreateIndex
CREATE UNIQUE INDEX "UserGroup_name_key" ON "UserGroup"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Order_orderNumber_key" ON "Order"("orderNumber");

-- CreateIndex
CREATE INDEX "Order_orderNumber_idx" ON "Order"("orderNumber");

-- CreateIndex
CREATE INDEX "Order_userId_idx" ON "Order"("userId");

-- CreateIndex
CREATE INDEX "Order_orderStatus_idx" ON "Order"("orderStatus");

-- CreateIndex
CREATE INDEX "Order_createdAt_idx" ON "Order"("createdAt");

-- CreateIndex
CREATE INDEX "Order_placedAt_orderStatus_idx" ON "Order"("placedAt", "orderStatus");

-- CreateIndex
CREATE INDEX "Order_userId_orderStatus_createdAt_idx" ON "Order"("userId", "orderStatus", "createdAt");

-- CreateIndex
CREATE INDEX "Order_orderStatus_paymentStatus_idx" ON "Order"("orderStatus", "paymentStatus");

-- CreateIndex
CREATE INDEX "Order_isActive_deletedAt_idx" ON "Order"("isActive", "deletedAt");

-- CreateIndex
CREATE INDEX "Order_archivedAt_idx" ON "Order"("archivedAt");

-- CreateIndex
CREATE INDEX "OrderItem_orderId_idx" ON "OrderItem"("orderId");

-- CreateIndex
CREATE INDEX "OrderItem_productId_idx" ON "OrderItem"("productId");

-- CreateIndex
CREATE INDEX "OrderItem_orderId_createdAt_idx" ON "OrderItem"("orderId", "createdAt");

-- CreateIndex
CREATE INDEX "OrderItem_updatedBy_idx" ON "OrderItem"("updatedBy");

-- CreateIndex
CREATE INDEX "OrderItem_version_idx" ON "OrderItem"("version");

-- CreateIndex
CREATE UNIQUE INDEX "OrderItem_orderId_productId_key" ON "OrderItem"("orderId", "productId");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_orderId_idx" ON "OrderStatusHistory"("orderId");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_orderId_createdAt_idx" ON "OrderStatusHistory"("orderId", "createdAt");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_orderStatus_idx" ON "OrderStatusHistory"("orderStatus");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_shipmentStatus_idx" ON "OrderStatusHistory"("shipmentStatus");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_paymentStatus_idx" ON "OrderStatusHistory"("paymentStatus");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_changedBy_idx" ON "OrderStatusHistory"("changedBy");

-- CreateIndex
CREATE UNIQUE INDEX "OrderPostPurchaseAction_actionNumber_key" ON "OrderPostPurchaseAction"("actionNumber");

-- CreateIndex
CREATE UNIQUE INDEX "OrderPostPurchaseAction_returnId_key" ON "OrderPostPurchaseAction"("returnId");

-- CreateIndex
CREATE UNIQUE INDEX "OrderPostPurchaseAction_serviceRequestId_key" ON "OrderPostPurchaseAction"("serviceRequestId");

-- CreateIndex
CREATE INDEX "OrderPostPurchaseAction_userId_idx" ON "OrderPostPurchaseAction"("userId");

-- CreateIndex
CREATE INDEX "OrderPostPurchaseAction_orderItemId_idx" ON "OrderPostPurchaseAction"("orderItemId");

-- CreateIndex
CREATE INDEX "OrderPostPurchaseAction_type_idx" ON "OrderPostPurchaseAction"("type");

-- CreateIndex
CREATE INDEX "OrderPostPurchaseAction_status_idx" ON "OrderPostPurchaseAction"("status");

-- CreateIndex
CREATE UNIQUE INDEX "OrderPostPurchaseAction_orderItemId_status_key" ON "OrderPostPurchaseAction"("orderItemId", "status");

-- CreateIndex
CREATE INDEX "Wishlist_userId_idx" ON "Wishlist"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Wishlist_userId_productCode_key" ON "Wishlist"("userId", "productCode");

-- CreateIndex
CREATE INDEX "ShippingAddress_phoneNumber_idx" ON "ShippingAddress"("phoneNumber");

-- CreateIndex
CREATE INDEX "BillingAddress_cui_idx" ON "BillingAddress"("cui");

-- CreateIndex
CREATE INDEX "BillingAddress_iban_idx" ON "BillingAddress"("iban");

-- CreateIndex
CREATE INDEX "Banner_placement_position_isActive_idx" ON "Banner"("placement", "position", "isActive");

-- CreateIndex
CREATE INDEX "Banner_startDate_endDate_idx" ON "Banner"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "Banner_deviceTarget_idx" ON "Banner"("deviceTarget");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel1_name_key" ON "CategoryLevel1"("name");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel2_name_level1Id_key" ON "CategoryLevel2"("name", "level1Id");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel3_familyCode_key" ON "CategoryLevel3"("familyCode");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel3_slug_key" ON "CategoryLevel3"("slug");

-- CreateIndex
CREATE INDEX "CategoryLevel3_familyCode_idx" ON "CategoryLevel3"("familyCode");

-- CreateIndex
CREATE INDEX "CategoryLevel3_slug_idx" ON "CategoryLevel3"("slug");

-- CreateIndex
CREATE INDEX "CategoryLevel3_displayOrder_idx" ON "CategoryLevel3"("displayOrder");

-- CreateIndex
CREATE INDEX "CategoryLevel3_productCount_idx" ON "CategoryLevel3"("productCount");

-- CreateIndex
CREATE INDEX "CategoryLevel3_isActive_deletedAt_idx" ON "CategoryLevel3"("isActive", "deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "Brand_name_key" ON "Brand"("name");

-- CreateIndex
CREATE UNIQUE INDEX "VehicleModel_name_key" ON "VehicleModel"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ProductClass_classCode_key" ON "ProductClass"("classCode");

-- CreateIndex
CREATE INDEX "ProductClass_classCode_idx" ON "ProductClass"("classCode");

-- CreateIndex
CREATE UNIQUE INDEX "Product_Material_Number_key" ON "Product"("Material_Number");

-- CreateIndex
CREATE INDEX "Product_HasDiscount_FinalPrice_idx" ON "Product"("HasDiscount", "FinalPrice");

-- CreateIndex
CREATE INDEX "Product_categoryLevel3Id_HasDiscount_idx" ON "Product"("categoryLevel3Id", "HasDiscount");

-- CreateIndex
CREATE INDEX "Product_categoryLevel3Id_FinalPrice_idx" ON "Product"("categoryLevel3Id", "FinalPrice");

-- CreateIndex
CREATE INDEX "Product_priceRange_HasDiscount_idx" ON "Product"("priceRange", "HasDiscount");

-- CreateIndex
CREATE INDEX "Product_discountPercentage_idx" ON "Product"("discountPercentage");

-- CreateIndex
CREATE INDEX "Product_Material_Number_idx" ON "Product"("Material_Number");

-- CreateIndex
CREATE INDEX "Product_Material_Group_idx" ON "Product"("Material_Group");

-- CreateIndex
CREATE INDEX "Product_Parts_Class_idx" ON "Product"("Parts_Class");

-- CreateIndex
CREATE INDEX "Product_Material_Group_Parts_Class_idx" ON "Product"("Material_Group", "Parts_Class");

-- CreateIndex
CREATE INDEX "Product_categoryLevel3Id_Material_Group_idx" ON "Product"("categoryLevel3Id", "Material_Group");

-- CreateIndex
CREATE INDEX "Product_PretAM_HasDiscount_idx" ON "Product"("PretAM", "HasDiscount");

-- CreateIndex
CREATE INDEX "Product_IsOnLandingPage_categoryLevel3Id_idx" ON "Product"("IsOnLandingPage", "categoryLevel3Id");

-- CreateIndex
CREATE INDEX "Product_createdAt_categoryLevel3Id_idx" ON "Product"("createdAt", "categoryLevel3Id");

-- CreateIndex
CREATE INDEX "Product_Material_Number_normalized_idx" ON "Product"("Material_Number_normalized");

-- CreateIndex
CREATE INDEX "Product_Description_Local_normalized_idx" ON "Product"("Description_Local_normalized");

-- CreateIndex
CREATE INDEX "Product_stockStatus_idx" ON "Product"("stockStatus");

-- CreateIndex
CREATE INDEX "Product_isActive_deletedAt_idx" ON "Product"("isActive", "deletedAt");

-- CreateIndex
CREATE INDEX "Product_updatedBy_idx" ON "Product"("updatedBy");

-- CreateIndex
CREATE INDEX "Product_version_idx" ON "Product"("version");

-- CreateIndex
CREATE INDEX "ProductPriceHistory_productId_idx" ON "ProductPriceHistory"("productId");

-- CreateIndex
CREATE INDEX "ProductPriceHistory_productId_createdAt_idx" ON "ProductPriceHistory"("productId", "createdAt");

-- CreateIndex
CREATE INDEX "ProductPriceHistory_createdAt_idx" ON "ProductPriceHistory"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "Discount_name_key" ON "Discount"("name");

-- CreateIndex
CREATE INDEX "Discount_active_idx" ON "Discount"("active");

-- CreateIndex
CREATE INDEX "Discount_name_idx" ON "Discount"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ProductDiscount_productId_key" ON "ProductDiscount"("productId");

-- CreateIndex
CREATE INDEX "ProductDiscount_discountId_idx" ON "ProductDiscount"("discountId");

-- CreateIndex
CREATE INDEX "ProductAttribute_key_value_idx" ON "ProductAttribute"("key", "value");

-- CreateIndex
CREATE INDEX "ProductAttribute_Material_Number_idx" ON "ProductAttribute"("Material_Number");

-- CreateIndex
CREATE UNIQUE INDEX "ProductAttribute_Material_Number_key_key" ON "ProductAttribute"("Material_Number", "key");

-- CreateIndex
CREATE INDEX "ProductAttributeHistory_Material_Number_idx" ON "ProductAttributeHistory"("Material_Number");

-- CreateIndex
CREATE UNIQUE INDEX "Return_returnNumber_key" ON "Return"("returnNumber");

-- CreateIndex
CREATE INDEX "Return_orderId_idx" ON "Return"("orderId");

-- CreateIndex
CREATE INDEX "Return_createdAt_idx" ON "Return"("createdAt");

-- CreateIndex
CREATE INDEX "Return_returnNumber_idx" ON "Return"("returnNumber");

-- CreateIndex
CREATE INDEX "Return_status_idx" ON "Return"("status");

-- CreateIndex
CREATE INDEX "ReturnItem_returnId_idx" ON "ReturnItem"("returnId");

-- CreateIndex
CREATE INDEX "ReturnItem_orderItemId_idx" ON "ReturnItem"("orderItemId");

-- CreateIndex
CREATE UNIQUE INDEX "ReturnItem_returnId_orderItemId_key" ON "ReturnItem"("returnId", "orderItemId");

-- CreateIndex
CREATE INDEX "ReturnStatusHistory_returnId_idx" ON "ReturnStatusHistory"("returnId");

-- CreateIndex
CREATE INDEX "ReturnStatusHistory_returnId_createdAt_idx" ON "ReturnStatusHistory"("returnId", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "ServiceRequest_serviceNumber_key" ON "ServiceRequest"("serviceNumber");

-- CreateIndex
CREATE INDEX "ServiceRequest_userId_idx" ON "ServiceRequest"("userId");

-- CreateIndex
CREATE INDEX "ServiceRequest_status_idx" ON "ServiceRequest"("status");

-- CreateIndex
CREATE INDEX "ServiceRequest_createdAt_idx" ON "ServiceRequest"("createdAt");

-- CreateIndex
CREATE INDEX "ServiceStatusHistory_serviceRequestId_idx" ON "ServiceStatusHistory"("serviceRequestId");

-- CreateIndex
CREATE INDEX "_UserToGroup_B_index" ON "_UserToGroup"("B");

-- AddForeignKey
ALTER TABLE "UserNotificationPreference" ADD CONSTRAINT "UserNotificationPreference_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserNotificationSetting" ADD CONSTRAINT "UserNotificationSetting_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserNotification" ADD CONSTRAINT "UserNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserNotification" ADD CONSTRAINT "UserNotification_relatedOrderId_fkey" FOREIGN KEY ("relatedOrderId") REFERENCES "Order"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserNotification" ADD CONSTRAINT "UserNotification_relatedActionId_fkey" FOREIGN KEY ("relatedActionId") REFERENCES "OrderPostPurchaseAction"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserNotification" ADD CONSTRAINT "UserNotification_relatedProductId_fkey" FOREIGN KEY ("relatedProductId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserNotificationDelivery" ADD CONSTRAINT "UserNotificationDelivery_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "UserNotification"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAuditLog" ADD CONSTRAINT "UserAuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserSession" ADD CONSTRAINT "UserSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "UserGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_billingAddressId_fkey" FOREIGN KEY ("billingAddressId") REFERENCES "BillingAddress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_shippingAddressId_fkey" FOREIGN KEY ("shippingAddressId") REFERENCES "ShippingAddress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderStatusHistory" ADD CONSTRAINT "OrderStatusHistory_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderPostPurchaseAction" ADD CONSTRAINT "OrderPostPurchaseAction_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "OrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderPostPurchaseAction" ADD CONSTRAINT "OrderPostPurchaseAction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderPostPurchaseAction" ADD CONSTRAINT "OrderPostPurchaseAction_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderPostPurchaseAction" ADD CONSTRAINT "OrderPostPurchaseAction_serviceRequestId_fkey" FOREIGN KEY ("serviceRequestId") REFERENCES "ServiceRequest"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Wishlist" ADD CONSTRAINT "Wishlist_productCode_fkey" FOREIGN KEY ("productCode") REFERENCES "Product"("Material_Number") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Wishlist" ADD CONSTRAINT "Wishlist_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShippingAddress" ADD CONSTRAINT "ShippingAddress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BillingAddress" ADD CONSTRAINT "BillingAddress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CategoryLevel2" ADD CONSTRAINT "CategoryLevel2_level1Id_fkey" FOREIGN KEY ("level1Id") REFERENCES "CategoryLevel1"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CategoryLevel3" ADD CONSTRAINT "CategoryLevel3_level2Id_fkey" FOREIGN KEY ("level2Id") REFERENCES "CategoryLevel2"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductClass" ADD CONSTRAINT "ProductClass_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "Brand"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductClassVehicleModel" ADD CONSTRAINT "ProductClassVehicleModel_productClassId_fkey" FOREIGN KEY ("productClassId") REFERENCES "ProductClass"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductClassVehicleModel" ADD CONSTRAINT "ProductClassVehicleModel_vehicleModelId_fkey" FOREIGN KEY ("vehicleModelId") REFERENCES "VehicleModel"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_classId_fkey" FOREIGN KEY ("classId") REFERENCES "ProductClass"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_categoryLevel3Id_fkey" FOREIGN KEY ("categoryLevel3Id") REFERENCES "CategoryLevel3"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductPriceHistory" ADD CONSTRAINT "ProductPriceHistory_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductHistory" ADD CONSTRAINT "ProductHistory_Material_Number_fkey" FOREIGN KEY ("Material_Number") REFERENCES "Product"("Material_Number") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductDiscount" ADD CONSTRAINT "ProductDiscount_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductDiscount" ADD CONSTRAINT "ProductDiscount_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscountHistory" ADD CONSTRAINT "DiscountHistory_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttribute" ADD CONSTRAINT "ProductAttribute_Material_Number_fkey" FOREIGN KEY ("Material_Number") REFERENCES "Product"("Material_Number") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttributeHistory" ADD CONSTRAINT "ProductAttributeHistory_Material_Number_fkey" FOREIGN KEY ("Material_Number") REFERENCES "Product"("Material_Number") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "ShippingAddress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnItem" ADD CONSTRAINT "ReturnItem_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnItem" ADD CONSTRAINT "ReturnItem_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "OrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnStatusHistory" ADD CONSTRAINT "ReturnStatusHistory_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "ShippingAddress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceStatusHistory" ADD CONSTRAINT "ServiceStatusHistory_serviceRequestId_fkey" FOREIGN KEY ("serviceRequestId") REFERENCES "ServiceRequest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserToGroup" ADD CONSTRAINT "_UserToGroup_A_fkey" FOREIGN KEY ("A") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserToGroup" ADD CONSTRAINT "_UserToGroup_B_fkey" FOREIGN KEY ("B") REFERENCES "UserGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;
