import PriceHistoryComponent from "@/app/components/product/PriceHistory";
import prisma from "@/app/utils/db";
import { requireAdmin } from "@/lib/auth-utils";


export default async function PriceHistoryRoute({ params }: { params: Promise<{ material_number: string }> }) {
    await requireAdmin();
    const paramsObject = await params;
    const materialNumber = paramsObject.material_number;    //All params are strings and need to be parsed
    const priceHistory = await prisma.productPriceHistory.findMany({
      where: { 
        product: {
          Material_Number: materialNumber
        }
      },
      orderBy: {
        createdAt: "desc"
      }
    });

    return (
        <div className="p-4">
          <h1 className="text-2xl font-bold mb-4">Price History for {materialNumber}</h1>
          <PriceHistoryComponent priceHistory={priceHistory} />
        </div>
    )
}
