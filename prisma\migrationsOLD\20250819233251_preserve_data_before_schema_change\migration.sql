-- 1) Add temp columns to retain data during refactor
ALTER TABLE "Order" ADD COLUMN "temp_hasReturns" BOOLEAN;
ALTER TABLE "Order" ADD COLUMN "temp_hasServiceRequests" BOOLEAN;

ALTER TABLE "Return" ADD COLUMN "temp_additionalNotes" TEXT;
ALTER TABLE "Return" ADD COLUMN "temp_reason" TEXT;

ALTER TABLE "ServiceRequest" ADD COLUMN "temp_orderId" TEXT;
ALTER TABLE "ServiceRequest" ADD COLUMN "temp_orderItemId" TEXT;

-- 2) Copy existing values
UPDATE "Order" SET "temp_hasReturns" = "hasReturns";
UPDATE "Order" SET "temp_hasServiceRequests" = "hasServiceRequests";

UPDATE "Return" SET "temp_additionalNotes" = "additionalNotes";
UPDATE "Return" SET "temp_reason" = "reason";

UPDATE "ServiceRequest" SET "temp_orderId" = "orderId";
UPDATE "ServiceRequest" SET "temp_orderItemId" = "orderItemId";
