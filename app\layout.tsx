import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Toaster } from "@/components/ui/sonner"
import Navbar from "./components/navbar/Navbar";
import "./globals.css";

import { Clerk<PERSON>rovider } from '@clerk/nextjs'

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Parts Database",
  description: "Automotive parts database and management system",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`${geistSans.variable} ${geistMono.variable} font-sans`}>
            <Navbar />
            <main className="min-h-screen px-8">
              {children}
            </main>
            <Toaster />
        </body>
      </html>
    </ClerkProvider>
  );
}



