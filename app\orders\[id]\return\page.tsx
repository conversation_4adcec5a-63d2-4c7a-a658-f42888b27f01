import Link from "next/link";
import { notFound } from "next/navigation";
import prisma from "@/app/utils/db";
import { auth } from "@clerk/nextjs/server";
import { Button } from "@/components/ui/button";
import ReturnForm from "@/app/components/return/ReturnForm";

export const metadata = {
  title: "Create Return | Parts Database",
  description: "Create a new return request",
};

export default async function CreateReturnPage({ 
  params 
}: { 
  params: { id: string } 
}) {
  const { userId } = await auth();
  if (!userId) {
    return notFound();
  }

  // Get the order with items
  const order = await prisma.order.findUnique({
    where: { 
      id: params.id,
      //userId: userId // Ensure the order belongs to the current user
    },
    select:{
      orderNumber: true,
      id: true,
      orderItems: {
        include: {
          product: true,
        }
      }
    }
  });

  if (!order) {
    return notFound();
  }

  // Format order items for the form
  const orderItems = order.orderItems.map(item => ({
    id: item.id,
    productName: item.product.Description_Local,
    productCode: item.product.Material_Number,
    quantity: item.quantity,
    price: item.price.toNumber()
  }));

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create Return Request</h1>
        <Button asChild variant="outline">
          <Link href={`/orders/${params.id}`}>Back to Order</Link>
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium mb-2">Order #{order.orderNumber}</h2>
          <p className="text-gray-600">
            Create a return request for items from this order.
          </p>
        </div>

        <ReturnForm orderId={order.id} orderItems={orderItems} />
      </div>
    </div>
  );
}