import { Metadata } from "next";
import { notFound } from "next/navigation";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { getGroupById } from "@/app/getData/groups/data";
import GroupPageClient from "./GroupPageClient";

interface GroupPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: GroupPageProps): Promise<Metadata> {
  const paramsObject = await params;
  const group = await getGroupById(paramsObject.id);
  
  return {
    title: group ? `${group.name} - Access Group` : "Group Not Found",
    description: group?.description || "Access group details",
  };
}

export default async function GroupPage({ params }: GroupPageProps) {
  await requireAdminOrModerator();
  const paramsObject = await params;
  const group = await getGroupById(paramsObject.id);

  if (!group) {
    notFound();
  }

  return <GroupPageClient group={group} />;
}
