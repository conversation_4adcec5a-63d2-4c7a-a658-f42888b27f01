"use server";

import { revalidatePath } from "next/cache";
import prisma from "@/app/utils/db";
import { logDebug, logError, logInfo } from "@/lib/logger";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { ReturnAction } from "./actions";
import { z } from "zod";
import { getUsersNotInGroup } from "../getData/user/data";

const groupSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  userAM: z.string().min(5, "User AM must be at least 5 characters").max(8, "User AM cannot exceed 8 characters").optional(),
});

type GroupFormValues = z.infer<typeof groupSchema>;

export async function createGroup(data: GroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    const validatedData = groupSchema.parse(data);

    // Check if group name already exists
    const existingGroup = await prisma.userGroup.findUnique({
      where: { name: validatedData.name }
    });

    if (existingGroup) {
      return {
        status: "ERROR",
        message: "A group with this name already exists",
      };
    }

    const group = await prisma.userGroup.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        createdBy: actorEmail,
        updatedBy: actorEmail,
      },
    });

    revalidatePath("/groups");
    logInfo(`Group created: ${group.id} by ${actorEmail}`);

    return {
      status: "SUCCESS",
      message: "Group created successfully",
    };
  } catch (error) {
    logError(`Error creating group by ${actorEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to create group",
    };
  }
}

export async function updateGroup(id: string, data: GroupFormValues): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    const validatedData = groupSchema.parse(data);

    // Check if another group with this name exists
    const existingGroup = await prisma.userGroup.findFirst({
      where: { 
        name: validatedData.name,
        NOT: { id }
      }
    });

    if (existingGroup) {
      return {
        status: "ERROR",
        message: "A group with this name already exists",
      };
    }

    await prisma.userGroup.update({
      where: { id },
      data: {
        name: validatedData.name,
        description: validatedData.description,
        updatedBy: actorEmail,
      },
    });

    revalidatePath("/groups");
    revalidatePath(`/groups/${id}`);
    logInfo(`Group updated: ${id} by ${actorEmail}`);

    return {
      status: "SUCCESS",
      message: "Group updated successfully",
    };
  } catch (error) {
    logError(`Error updating group ${id} by ${actorEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to update group",
    };
  }
}

export async function deleteGroup(id: string): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    // Check if group has users
    const group = await prisma.userGroup.findUnique({
      where: { id },
      include: { _count: { select: { users: true } } }
    });

    if (!group) {
      return {
        status: "ERROR",
        message: "Group not found",
      };
    }

    if (group._count.users > 0) {
      return {
        status: "ERROR",
        message: "Cannot delete group with active users. Remove all users first.",
      };
    }

    await prisma.userGroup.delete({
      where: { id },
    });

    revalidatePath("/groups");
    logInfo(`Group deleted: ${id} by ${actorEmail}`);

    return {
      status: "SUCCESS",
      message: "Group deleted successfully",
    };
  } catch (error) {
    logError(`Error deleting group ${id} by ${actorEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to delete group",
    };
  }
}

export async function deleteMemberFromGroup(userId: string, groupId: string): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const actorEmail = actor.email;

  try {
    await prisma.userGroup.update({
      where: { id: groupId },
      data: {
        users: {
          disconnect: { id: userId }
        }
      }
    });

    revalidatePath(`/groups/${groupId}`);
    logInfo(`User removed from group: ${userId} from ${groupId} by ${actorEmail}`);

    return {
      status: "SUCCESS",
      message: "User removed from group successfully",
    };
  } catch (error) {
    logError(`Error removing user from group: ${error} by ${actorEmail}`);
    return {
      status: "ERROR",
      message: "Failed to remove user from group",
    };
  }
}
