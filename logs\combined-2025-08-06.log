{"level":"info","message":"User removed from group: cmcukwkxz0008hxj4asrqq78p from cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-06 07:57:26"}
{"level":"info","message":"User cmcnevyak0006hx78b8yc8oz4 added to group cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-06 07:58:18"}
{"level":"info","message":"User cmcnevyak0006hx78b8yc8oz4 details retrieved for userEmail","timestamp":"2025-08-06 07:58:24"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz added to group cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-06 07:59:09"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 07:59:43"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz removed from group cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-06 08:04:07"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:04:07"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:04:08"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz added to group cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-06 08:04:58"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:04:58"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:04:58"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz added to group cmdyxh7c60002hxs8cvdzn82<NAME_EMAIL>","timestamp":"2025-08-06 08:05:03"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:05:03"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:05:03"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz removed from group cmdyxh7c60002hxs8cvdzn82<NAME_EMAIL>","timestamp":"2025-08-06 08:05:18"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:05:18"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:05:18"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz added to group cmdyxh7c60002hxs8cvdzn82<NAME_EMAIL>","timestamp":"2025-08-06 08:05:53"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:05:54"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:05:54"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz removed from group cmdyxh7c60002hxs8cvdzn82<NAME_EMAIL>","timestamp":"2025-08-06 08:06:42"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:06:42"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:06:43"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz removed from group cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-06 08:14:58"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:14:58"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:14:58"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:15:11"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz added to group cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-06 08:15:21"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:15:21"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 08:15:22"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 09:09:03"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 09:36:55"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 10:27:43"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 10:39:42"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 10:40:19"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:20:44"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-08-06 11:29:06"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:37:30"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:37:45"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-06 11:48:09"}
{"level":"info","message":"User cmcukwkxz0008hxj4asrqq78p details retrieved for userEmail","timestamp":"2025-08-06 11:48:32"}
{"level":"info","message":"User cmdwxot5n000shx0of0ymzh6z details retrieved for userEmail","timestamp":"2025-08-06 11:48:37"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:49:08"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:49:35"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"role\":{\"from\":\"fourLvlInregistratAB\",\"to\":\"fourLvlAdminAB\"},\"jobTitle\":{\"from\":null,\"to\":\"\"},\"department\":{\"from\":null,\"to\":\"\"},\"bio\":{\"from\":null,\"to\":\"\"},\"preferredLanguage\":{\"from\":null,\"to\":\"\"},\"timezone\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-08-06 11:50:38"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:50:38"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:50:39"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 11:50:54"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:01:07"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:01:07"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:01:07"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:43:32"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:43:33"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:43:33"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:43:34"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:50:49"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:51:56"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 12:53:04"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:06"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:07"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:07"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:34"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:35"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:35"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:36"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:34:37"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:37:24"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:37:24"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:37:24"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:41:59"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:42:17"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:42:41"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:44:14"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:54:14"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:54:14"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 13:54:14"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:21:42"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:21:43"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:21:43"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:30:26"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:30:26"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:30:27"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:31:08"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:31:08"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:31:09"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:41:59"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:41:59"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:41:59"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:42:36"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:42:36"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:42:36"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:49:18"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:49:18"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:49:18"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:57:19"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:57:19"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:57:19"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:57:22"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:58:22"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"lastName\":{\"from\":\"Oprea 123\",\"to\":\"Oprea 1233\"},\"suspensionReason\":{\"from\":null,\"to\":\"\"}}","timestamp":"2025-08-06 14:58:46"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:58:46"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:58:48"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:59:37"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"firstName\":{\"from\":\"Opreaa\",\"to\":\"Opreaa3\"}}","timestamp":"2025-08-06 14:59:44"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:59:44"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 14:59:46"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"userAM\":{\"from\":\"1001652\",\"to\":\"10016521\"}}","timestamp":"2025-08-06 15:00:11"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:00:11"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:00:11"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"userAM\":{\"from\":\"10016521\",\"to\":\"1001652\"}}","timestamp":"2025-08-06 15:00:25"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:00:25"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:00:26"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"role\":{\"from\":\"fourLvlAdminAB\",\"to\":\"fourLvlInregistratAB\"}}","timestamp":"2025-08-06 15:00:45"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:00:45"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:00:45"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:01:20"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:01:20"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:06:00"}
{"level":"error","message":"Clerk API error: [Error]\nMessage:Unprocessable Entity\nStatus:422\nSerialized errors: {\"code\":\"form_password_length_too_short\",\"message\":\"Passwords must be 12 characters or more.\",\"longMessage\":\"Passwords must be 12 characters or more.\",\"meta\":{\"paramName\":\"password\"}},{\"code\":\"form_password_no_special_char\",\"message\":\"Passwords must contain at least one of the following special characters: !\\\"#$%&'()*+,-./:;<=>?@[]^_`{|}~.\",\"longMessage\":\"Passwords must contain at least one of the following special characters: !\\\"#$%&'()*+,-./:;<=>?@[]^_`{|}~.\",\"meta\":{\"paramName\":\"password\"}}\nClerk Trace ID: c61932e4b20ca6145cdf11edb174641a for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 15:06:38"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:13:34"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:13:34"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:13:35"}
{"level":"error","message":"Clerk API error: [Error]\nMessage:Unprocessable Entity\nStatus:422\nSerialized errors: {\"code\":\"form_password_pwned\",\"message\":\"Password has been found in an online data breach. For account safety, please use a different password.\",\"longMessage\":\"Password has been found in an online data breach. For account safety, please use a different password.\",\"meta\":{\"paramName\":\"password\"}}\nClerk Trace ID: 211003cc214bdd6cc8025c2b7788ce46 for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 15:13:38"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"password\":{\"from\":\"[REDACTED]\",\"to\":\"[REDACTED]\"}}","timestamp":"2025-08-06 15:14:06"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:14:06"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:14:08"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"isSuspended\":{\"from\":false,\"to\":true}}","timestamp":"2025-08-06 15:15:16"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:15:16"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 15:15:16"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:00:46"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"isSuspended\":{\"from\":false,\"to\":true},\"suspensionReason\":{\"from\":\"\",\"to\":\"testare noua\"}}","timestamp":"2025-08-06 16:01:00"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:01:00"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:01:04"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:08:03"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"isActive\":{\"from\":true,\"to\":false}}","timestamp":"2025-08-06 16:08:15"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:08:15"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:08:16"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:08:56"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"preferences\":{\"from\":{\"newsletterOptIn\":true,\"smsNotifications\":false,\"pushNotifications\":false,\"emailNotifications\":true},\"to\":{\"newsletterOptIn\":true,\"smsNotifications\":true,\"pushNotifications\":true,\"emailNotifications\":true}}}","timestamp":"2025-08-06 16:09:11"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:09:11"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:09:12"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:11:11"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"preferences\":{\"from\":{\"newsletterOptIn\":true,\"smsNotifications\":true,\"pushNotifications\":true,\"emailNotifications\":true},\"to\":{\"newsletterOptIn\":true,\"smsNotifications\":false,\"pushNotifications\":true,\"emailNotifications\":true}}}","timestamp":"2025-08-06 16:11:15"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:11:15"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:11:16"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"isActive\":{\"from\":true,\"to\":false},\"inactiveReason\":{\"from\":\"fraier\",\"to\":\"test\"}}","timestamp":"2025-08-06 16:11:48"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:11:48"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:11:49"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:12:12"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"isActive\":{\"from\":false,\"to\":true}}","timestamp":"2025-08-06 16:12:16"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:12:16"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:12:17"}
{"level":"error","message":"[updateUser]Failed to update email in Clerk: [Error]\nMessage:Bad Request\nStatus:400\nSerialized errors: {\"code\":\"last_required_identification_deletion_failed\",\"message\":\"Deleting your last email address is prohibited\",\"longMessage\":\"You are required to maintain at least one email address in your account at all times\",\"meta\":{}}\nClerk Trace ID: 01cf73888586a94a76145dd70923826e for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 16:12:48"}
{"level":"error","message":"[updateUser]Failed to update email in Clerk: [Error]\nMessage:Bad Request\nStatus:400\nSerialized errors: {\"code\":\"last_required_identification_deletion_failed\",\"message\":\"Deleting your last email address is prohibited\",\"longMessage\":\"You are required to maintain at least one email address in your account at all times\",\"meta\":{}}\nClerk Trace ID: 88e719fcc2578dc4762eb39a2556a794 for user cmdcvxu00001<NAME_EMAIL>","timestamp":"2025-08-06 16:12:59"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:14:08"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:14:08"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:14:11"}
{"level":"info","message":"User updated successfully: cosmin_p@yahoo.<NAME_EMAIL> with changes: {\"email\":{\"from\":\"<EMAIL>\",\"to\":\"<EMAIL>\"}}","timestamp":"2025-08-06 16:14:19"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:14:19"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:14:20"}
{"level":"info","message":"User updated successfully: cosmin_pm@yahoo.<NAME_EMAIL> with changes: {\"email\":{\"from\":\"<EMAIL>\",\"to\":\"<EMAIL>\"}}","timestamp":"2025-08-06 16:14:48"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:14:48"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:14:48"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:20:04"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:20:04"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:26:05"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:26:16"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:26:26"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:28:06"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:28:49"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:37:29"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:37:29"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:39:43"}
{"level":"info","message":"User cmdcvxu00001chxeobbcoozxz details retrieved for userEmail","timestamp":"2025-08-06 16:40:16"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-06 16:43:28"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-06 16:44:10"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-06 16:44:11"}
{"level":"info","message":"User deleted: cosmin.oprea@automobilebavaria.<NAME_EMAIL>","timestamp":"2025-08-06 16:44:43"}
