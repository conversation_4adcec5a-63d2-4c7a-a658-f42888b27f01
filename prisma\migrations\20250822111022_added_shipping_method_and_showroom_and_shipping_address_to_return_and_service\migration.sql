/*
  Warnings:

  - Added the required column `method` to the `Return` table without a default value. This is not possible if the table is not empty.
  - Added the required column `method` to the `ServiceRequest` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Return" DROP CONSTRAINT "Return_addressId_fkey";

-- DropForeignKey
ALTER TABLE "ServiceRequest" DROP CONSTRAINT "ServiceRequest_addressId_fkey";

-- AlterTable
ALTER TABLE "Return" ADD COLUMN     "method" "ShippingMethod" NOT NULL,
ADD COLUMN     "showroomCode" "Showroom",
ALTER COLUMN "addressId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "ServiceRequest" ADD COLUMN     "method" "ShippingMethod" NOT NULL,
ADD COLUMN     "showroomCode" "Showroom",
ALTER COLUMN "addressId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "ShippingAddress"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "ShippingAddress"("id") ON DELETE SET NULL ON UPDATE CASCADE;
