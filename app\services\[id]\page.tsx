import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getServiceRequestById } from "@/app/getData/service/data";
import ServiceDetails from "@/app/components/service/ServiceDetails";
import { requireAdminOrModerator } from "@/lib/auth-utils";

interface ServiceDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: ServiceDetailsPageProps): Promise<Metadata> {
  const { id } = await params;
  const serviceRequest = await getServiceRequestById(id);

  if (!serviceRequest) {
    return {
      title: "Service Request Not Found",
    };
  }

  return {
    title: `Service Request ${serviceRequest.serviceNumber}`,
    description: `Details for service request ${serviceRequest.serviceNumber}`,
  };
}

export default async function ServiceDetailsPage({ params }: ServiceDetailsPageProps) {
  await requireAdminOrModerator();

  const { id } = await params;
  const serviceRequest = await getServiceRequestById(id);

  if (!serviceRequest) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <ServiceDetails serviceRequest={serviceRequest} />
    </div>
  );
}
