import Link from "next/link";
import { notFound } from "next/navigation";
import { getReturnById } from "@/app/getData/return/data";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { Button } from "@/components/ui/button";
import ReturnDetails from "@/app/components/return/ReturnDetails";
import ReturnStatusUpdate from "@/app/components/return/ReturnStatusUpdate";

export const metadata = {
  title: "Return Details | Parts Database",
  description: "View and manage return details",
};

export default async function ReturnDetailPage({
  params
}: {
  params: Promise<{ id: string }>
}) {
  await requireAdminOrModerator();
  const { id } = await params;
  const returnData = await getReturnById(id);
  
  if (!returnData) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Return Details</h1>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/returns">Back to Returns</Link>
          </Button>
          <Button asChild>
            <Link href={`/orders/${returnData.orderId}`}>View Order</Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ReturnDetails returnData={returnData} />
        </div>
        <div>
          <ReturnStatusUpdate 
            returnId={returnData.id} 
            currentStatus={returnData.status} 
          />
        </div>
      </div>
    </div>
  );
}