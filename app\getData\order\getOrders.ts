
import prisma from "@/app/utils/db";
import { Order, OrderStatus } from "@/generated/prisma";
import { getCurrentUser } from "../user/data";
import { logError } from "@/lib/logger";
import { z } from "zod";
import { RawOrders, ReturnOrders } from "@/app/types/order";

export async function getOrders(options?: {
  limit?: number;
  offset?: number;
  status?: OrderStatus;
  userId?: string;
}){

  const user = await getCurrentUser()
  const userEmail = user?.email

  try{
  //validate parameters with zod and create the schema
  const getOrdersSchema = z.object({
    limit: z.number().min(1).max(100).optional(),
    offset: z.number().min(0).optional(),
    status: z.enum(Object.values(OrderStatus) as [string, ...string[]]).optional(),
  }).optional();
  
  const validatedOptions = getOrdersSchema.safeParse(options);
  if (!validatedOptions.success) {  
    logError(`getOrders -> Error at parsing: ${validatedOptions.error} by ${userEmail}`)
    return []
  }

  const { limit = 50, offset = 0, status, userId } = options || {};

  // const orders = await prisma.order.findMany({
  //   where,
  //   orderBy: {
  //     createdAt: "desc",
  //   },
  //   include: {
  //     user: {
  //       select: {
  //         firstName: true,
  //         lastName: true,
  //         email: true,
  //       },
  //     },
  //     billingAddress: true,
  //     shippingAddress: true,
  //   },
  //   take: limit,
  //   skip: offset,
  // });
  const orders = await prisma.order.findMany({
  where: {
    isActive: true,
    ...(status && { orderStatus: status }),
    //...(userId && { userId }),
  },
  select: {
    id: true,
    orderNumber: true,
    amount: true,
    isPaid: true,
    orderStatus: true,
    paymentStatus: true,
    createdAt: true,
    vin: true,
    notes: true,
    user: {
      select: {
        firstName: true,
        lastName: true,
        email: true,
      },
    },
  },
  orderBy: [
    { createdAt: "desc" },
    //{ id: "desc" }, // secondary sort for stable ordering when timestamps tie
  ],
  take: limit,
  skip: offset,
}) as RawOrders[];

  const typedOrders = orders.map((o) => ({
    user: {
      email: o.user.email,
      firstName: o.user.firstName,
      lastName: o.user.lastName,
    },
    id: o.id,
    orderNumber: o.orderNumber,
    amount: o.amount.toNumber(), // Convert Decimal to number
    isPaid: o.isPaid,
    vin: o.vin,
    notes: o.notes,
    orderStatus: o.orderStatus,
    paymentStatus: o.paymentStatus,
    createdAt: o.createdAt.toISOString(), // Convert Date to ISO string
  })) as ReturnOrders[];


  if (!typedOrders) {
    logError(`getOrders -> No orders found for ${userEmail}`)
    return [];
  }

  return typedOrders;
  }catch(e){
    logError(`getOrders -> Unexpected error: ${e} for ${userEmail}`)
    return []
  }
}

export async function getOrderById(id: string) {
  const user = await getCurrentUser()
  const userEmail = user?.email
  try{
    //validate parameter with zod
    const validatedId = z.string().cuid().safeParse(id);
    if (!validatedId.success) {
      logError(`getOrderById -> Error at parsing: ${validatedId.error} by ${userEmail}`)
      return null;
    }
    id = validatedId.data


    const order = await prisma.order.findUnique({
      where: {
        id,
        isActive: true,
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        billingAddress: true,
        shippingAddress: true,
        orderItems: {
          include: {
            product: true,
          },
        },
        statusHistory: {
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });
   
    if (!order) {
      logError(`getOrderById -> Order not found with ID ${id} by ${userEmail}`)
      return null;
    }
    return order;
  }
  catch(e){
    logError(`getOrderById -> Unexpected error: ${e} for ${userEmail}`)
    return null
  }
}
