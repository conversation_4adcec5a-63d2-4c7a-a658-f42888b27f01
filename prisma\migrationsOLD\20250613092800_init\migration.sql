-- CreateEnum
CREATE TYPE "NotificationType" AS ENUM ('INFO', 'SUCCESS', 'WARNING', 'ERROR');

-- CreateEnum
CREATE TYPE "BannerPlacement" AS ENUM ('HOME', 'CATEGORY', 'PRODUCT', 'CHECKOUT', 'SIDEBAR', 'HEADER', 'FOOTER', 'POPUP');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "DeviceTarget" AS ENUM ('ALL', 'DESKTOP', 'MOBILE', 'TABLET');

-- CreateEnum
CREATE TYPE "ChangeType" AS ENUM ('CRON_DISCOUNT_EXPIRED', 'MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS', 'INSERT', 'UPDATE', 'DELETE', 'ORDER', 'DISCOUNT', 'PRODUCT_ADDED', 'PRODUCT_DELETE_ALL', 'PRODUCT_DELETE', 'PRODUCT_ADD_TO_DISCOUNT_WITH_CSV', 'ADDED_TO_DISCOUNT', 'DELETED_FROM_DISCOUNT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "DiscountType" AS ENUM ('PERCENTAGE', 'FIXED_AMOUNT', 'NEW_PRICE');

-- CreateEnum
CREATE TYPE "MisterMiss" AS ENUM ('Dl', 'Dna');

-- CreateEnum
CREATE TYPE "Rol" AS ENUM ('administAB', 'moderatorAB', 'inregistratAB', 'fourLvlAdminAB', 'fourLvlInregistratAB', 'angajatAB');

-- CreateEnum
CREATE TYPE "Showroom" AS ENUM ('CJ', 'BV', 'TM', 'AR', 'BAC', 'BAN', 'OTP', 'MIL', 'TGM', 'JIL', 'CT', 'CRA', 'SB');

-- CreateEnum
CREATE TYPE "OrderStatus" AS ENUM ('plasata', 'procesare', 'confirmata', 'pregatita', 'expediata', 'livrata', 'completa', 'anulata', 'stornata', 'returnata', 'partiala');

-- CreateEnum
CREATE TYPE "ShippingMethod" AS ENUM ('curier', 'showroom');

-- CreateEnum
CREATE TYPE "ShipmentStatus" AS ENUM ('asteptare', 'prelucrare', 'pregatit', 'expediat', 'tranzit', 'livrat', 'esuat', 'intors', 'anulat', 'partial');

-- CreateEnum
CREATE TYPE "PaymentMethod" AS ENUM ('ramburs', 'card', 'transfer', 'laTermen');

-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('asteptare', 'succes', 'esuat', 'rambursat', 'partial_rambursat', 'contestat');

-- CreateEnum
CREATE TYPE "StockStatus" AS ENUM ('IN_STOCK', 'LOW_STOCK', 'OUT_OF_STOCK', 'DISCONTINUED', 'UNKNOWN');

-- CreateEnum
CREATE TYPE "ReturnStatus" AS ENUM ('requested', 'approved', 'rejected', 'awaitingReceipt', 'received', 'inspected', 'refundIssued', 'completed', 'cancelled');

-- CreateEnum
CREATE TYPE "ReturnReason" AS ENUM ('wrongItem', 'defective', 'damaged', 'notAsDescribed', 'noLongerWanted', 'other');

-- CreateEnum
CREATE TYPE "ReturnItemReason" AS ENUM ('wrongItem', 'defective', 'damaged', 'notAsDescribed', 'noLongerWanted', 'other');

-- CreateEnum
CREATE TYPE "ItemCondition" AS ENUM ('asDescribed', 'damaged', 'opened', 'used', 'missingParts');

-- CreateEnum
CREATE TYPE "InspectionResult" AS ENUM ('approved', 'rejected', 'partiallyApproved');

-- CreateEnum
CREATE TYPE "RefundMethod" AS ENUM ('originalPayment', 'storeCredit', 'bankTransfer');

-- CreateEnum
CREATE TYPE "ServiceType" AS ENUM ('repair', 'maintenance', 'warranty', 'installation', 'inspection', 'other');

-- CreateEnum
CREATE TYPE "ServiceStatus" AS ENUM ('requested', 'scheduled', 'inProgress', 'diagnosisComplete', 'awaitingParts', 'awaitingApproval', 'completed', 'cancelled', 'delivered');

-- CreateEnum
CREATE TYPE "ServiceItemType" AS ENUM ('part', 'labor', 'fee', 'other');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "profileImage" TEXT NOT NULL,
    "userAM" TEXT,
    "phoneNumber" TEXT,
    "newsletterOptIn" BOOLEAN NOT NULL DEFAULT false,
    "externalId" TEXT,
    "externalProvider" TEXT,
    "emailVerified" TIMESTAMP(3),
    "twoFactorEnabled" BOOLEAN NOT NULL DEFAULT false,
    "salutation" "MisterMiss",
    "role" "Rol" NOT NULL DEFAULT 'inregistratAB',
    "jobTitle" TEXT,
    "department" TEXT,
    "bio" TEXT,
    "preferredLanguage" TEXT,
    "timezone" TEXT,
    "permissions" TEXT[],
    "lastLoginAt" TIMESTAMP(3),
    "loginCount" INTEGER NOT NULL DEFAULT 0,
    "lastActivityAt" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isSuspended" BOOLEAN NOT NULL DEFAULT false,
    "suspensionReason" TEXT,
    "deletedAt" TIMESTAMP(3),
    "passwordChangedAt" TIMESTAMP(3),
    "loginAttempts" INTEGER NOT NULL DEFAULT 0,
    "lockoutUntil" TIMESTAMP(3),
    "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "pushNotifications" BOOLEAN NOT NULL DEFAULT false,
    "smsNotifications" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserSession" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "deviceId" TEXT,
    "location" TEXT,
    "lastActiveAt" TIMESTAMP(3),
    "isRevoked" BOOLEAN NOT NULL DEFAULT false,
    "revokedReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserGroup" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "permissions" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,
    "updatedBy" TEXT,

    CONSTRAINT "UserGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserSecurityQuestion" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserSecurityQuestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserNotification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "type" "NotificationType" NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "link" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserAuditLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "action" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT,
    "details" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "performedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Order" (
    "id" TEXT NOT NULL,
    "orderNumber" TEXT NOT NULL,
    "amount" DECIMAL(15,2) NOT NULL,
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "vin" TEXT,
    "invoiceAM" TEXT,
    "updatesEnabled" BOOLEAN NOT NULL DEFAULT true,
    "terms" BOOLEAN NOT NULL DEFAULT true,
    "orderStatus" "OrderStatus" NOT NULL DEFAULT 'plasata',
    "paymentStatus" "PaymentStatus" NOT NULL DEFAULT 'asteptare',
    "paymentMethod" "PaymentMethod" NOT NULL DEFAULT 'ramburs',
    "shippingMethod" "ShippingMethod" NOT NULL DEFAULT 'curier',
    "shipmentStatus" "ShipmentStatus" NOT NULL DEFAULT 'asteptare',
    "showroom" "Showroom",
    "placedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "cancelledAt" TIMESTAMP(3),
    "shippingProcessedAt" TIMESTAMP(3),
    "shippedAt" TIMESTAMP(3),
    "deliveredAt" TIMESTAMP(3),
    "paidAt" TIMESTAMP(3),
    "refundedAt" TIMESTAMP(3),
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),
    "archivedAt" TIMESTAMP(3),
    "notes" TEXT,
    "billingAddressId" TEXT NOT NULL,
    "shippingAddressId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "hasReturns" BOOLEAN NOT NULL DEFAULT false,
    "hasServiceRequests" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderItem" (
    "id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "notes" TEXT,
    "notesToInvoice" BOOLEAN NOT NULL DEFAULT false,
    "vinOrderItem" TEXT,
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "orderId" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrderItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Wishlist" (
    "id" TEXT NOT NULL,
    "productCode" TEXT NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "Wishlist_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ShippingAddress" (
    "id" TEXT NOT NULL,
    "fullName" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "county" TEXT NOT NULL,
    "phoneNumber" TEXT NOT NULL,
    "notes" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShippingAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BillingAddress" (
    "id" TEXT NOT NULL,
    "fullName" TEXT NOT NULL,
    "companyName" TEXT,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "county" TEXT NOT NULL,
    "cui" TEXT,
    "bank" TEXT,
    "iban" TEXT,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "userId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BillingAddress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Banner" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "subtitle" TEXT,
    "imageUrl" TEXT NOT NULL,
    "mobileImageUrl" TEXT,
    "callToAction" TEXT,
    "buttonText" TEXT,
    "description" TEXT,
    "url" TEXT,
    "placement" "BannerPlacement" NOT NULL DEFAULT 'HOME',
    "position" INTEGER NOT NULL DEFAULT 0,
    "width" TEXT,
    "height" TEXT,
    "backgroundColor" TEXT,
    "textColor" TEXT,
    "targetAudience" TEXT,
    "deviceTarget" "DeviceTarget" NOT NULL DEFAULT 'ALL',
    "startDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endDate" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "impressions" INTEGER NOT NULL DEFAULT 0,
    "clicks" INTEGER NOT NULL DEFAULT 0,
    "conversionRate" DOUBLE PRECISION,
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Banner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategoryLevel1" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT false,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CategoryLevel1_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategoryLevel2" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT false,
    "imageUrl" TEXT,
    "level1Id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CategoryLevel2_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategoryLevel3" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT false,
    "familyCode" TEXT,
    "imageUrl" TEXT,
    "slug" TEXT,
    "metaTitle" TEXT,
    "metaDescription" TEXT,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "productCount" INTEGER NOT NULL DEFAULT 0,
    "lastProductAdded" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),
    "level2Id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CategoryLevel3_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Brand" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "nameRO" TEXT,
    "afisat" BOOLEAN NOT NULL DEFAULT true,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Brand_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VehicleModel" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "VehicleModel_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductClass" (
    "id" TEXT NOT NULL,
    "classCode" TEXT NOT NULL,
    "brandId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductClass_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductClassVehicleModel" (
    "productClassId" TEXT NOT NULL,
    "vehicleModelId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductClassVehicleModel_pkey" PRIMARY KEY ("productClassId","vehicleModelId")
);

-- CreateTable
CREATE TABLE "Product" (
    "id" TEXT NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "Net_Weight" TEXT,
    "Description_Local" TEXT,
    "Base_Unit_Of_Measur" TEXT,
    "Cross_Plant" TEXT,
    "New_Material" TEXT,
    "PretAM" DECIMAL(15,2),
    "FinalPrice" DECIMAL(15,2),
    "HasDiscount" BOOLEAN NOT NULL DEFAULT false,
    "activeDiscountType" "DiscountType",
    "activeDiscountValue" DECIMAL(10,2),
    "discountPercentage" DECIMAL(5,2),
    "priceRange" TEXT,
    "ImageUrl" TEXT[] DEFAULT ARRAY['http://asdasdasd.com']::TEXT[],
    "IsOnLandingPage" BOOLEAN NOT NULL DEFAULT false,
    "Material_Number_normalized" TEXT,
    "Description_Local_normalized" TEXT,
    "stockStatus" "StockStatus" NOT NULL DEFAULT 'UNKNOWN',
    "createdBy" TEXT,
    "updatedBy" TEXT,
    "version" INTEGER NOT NULL DEFAULT 1,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "deletedAt" TIMESTAMP(3),
    "Parts_Class" TEXT DEFAULT 'undefined-class',
    "classId" TEXT,
    "Material_Group" TEXT DEFAULT 'undefined-category',
    "categoryLevel3Id" TEXT,
    "isServiceable" BOOLEAN NOT NULL DEFAULT false,
    "warrantyMonths" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Product_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PriceHistory" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "oldPretAM" DECIMAL(15,2),
    "newPretAM" DECIMAL(15,2),
    "oldFinalPrice" DECIMAL(15,2),
    "newFinalPrice" DECIMAL(15,2),
    "reason" TEXT,
    "source" TEXT,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PriceHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductHistory" (
    "id" UUID NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "snapshot" JSONB NOT NULL,
    "change_type" "ChangeType" NOT NULL,
    "version" INTEGER NOT NULL,
    "changed_by" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Discount" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "type" "DiscountType" NOT NULL,
    "value" DECIMAL(10,2) NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Discount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductDiscount" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductDiscount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DiscountHistory" (
    "id" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "snapshot" JSONB NOT NULL,
    "change_type" "ChangeType" NOT NULL,
    "version" INTEGER NOT NULL,
    "changed_by" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DiscountHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductAttribute" (
    "id" TEXT NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "key" TEXT,
    "value" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductAttribute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductAttributeHistory" (
    "id" TEXT NOT NULL,
    "Material_Number" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "snapshot" JSONB NOT NULL,
    "change_type" "ChangeType" NOT NULL,
    "version" INTEGER NOT NULL,
    "changed_by" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductAttributeHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductView" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "userId" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "referrer" TEXT,
    "viewedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProductView_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SystemConfig" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SystemConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ArchivedOrder" (
    "id" TEXT NOT NULL,
    "originalId" TEXT NOT NULL,
    "amount" DECIMAL(15,2) NOT NULL,
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "vin" TEXT,
    "invoiceAM" TEXT,
    "updatesEnabled" BOOLEAN NOT NULL DEFAULT true,
    "terms" BOOLEAN NOT NULL DEFAULT true,
    "orderStatus" "OrderStatus" NOT NULL DEFAULT 'plasata',
    "paymentStatus" "PaymentStatus" NOT NULL DEFAULT 'asteptare',
    "paymentMethod" "PaymentMethod" NOT NULL DEFAULT 'ramburs',
    "shippingMethod" "ShippingMethod" NOT NULL DEFAULT 'curier',
    "shipmentStatus" "ShipmentStatus" NOT NULL DEFAULT 'asteptare',
    "showroom" "Showroom",
    "userId" TEXT NOT NULL,
    "billingAddressId" TEXT NOT NULL,
    "shippingAddressId" TEXT NOT NULL,
    "archivedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "archivedBy" TEXT,
    "archiveReason" TEXT,
    "originalCreatedAt" TIMESTAMP(3) NOT NULL,
    "originalUpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ArchivedOrder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OrderStatusHistory" (
    "id" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "orderStatus" "OrderStatus",
    "paymentStatus" "PaymentStatus",
    "shipmentStatus" "ShipmentStatus",
    "previousOrderStatus" "OrderStatus",
    "previousPaymentStatus" "PaymentStatus",
    "previousShipmentStatus" "ShipmentStatus",
    "reason" TEXT,
    "notes" TEXT,
    "changedBy" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "OrderStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Return" (
    "id" TEXT NOT NULL,
    "returnNumber" TEXT NOT NULL,
    "orderId" TEXT NOT NULL,
    "status" "ReturnStatus" NOT NULL DEFAULT 'requested',
    "reason" "ReturnReason" NOT NULL,
    "additionalNotes" TEXT,
    "isApproved" BOOLEAN,
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "refundAmount" DECIMAL(15,2),
    "refundMethod" "RefundMethod",
    "refundedAt" TIMESTAMP(3),
    "refundReference" TEXT,
    "returnShippingLabel" TEXT,
    "receivedAt" TIMESTAMP(3),
    "inspectedAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "updatedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Return_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReturnItem" (
    "id" TEXT NOT NULL,
    "returnId" TEXT NOT NULL,
    "orderItemId" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "reason" "ReturnItemReason" NOT NULL,
    "condition" "ItemCondition" NOT NULL DEFAULT 'asDescribed',
    "description" TEXT,
    "isReceived" BOOLEAN NOT NULL DEFAULT false,
    "isInspected" BOOLEAN NOT NULL DEFAULT false,
    "inspectionNotes" TEXT,
    "inspectionResult" "InspectionResult",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReturnItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ReturnStatusHistory" (
    "id" TEXT NOT NULL,
    "returnId" TEXT NOT NULL,
    "previousStatus" "ReturnStatus",
    "newStatus" "ReturnStatus" NOT NULL,
    "notes" TEXT,
    "changedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ReturnStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceRequest" (
    "id" TEXT NOT NULL,
    "serviceNumber" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "vin" TEXT,
    "vehicleModel" TEXT,
    "vehicleYear" INTEGER,
    "mileage" INTEGER,
    "type" "ServiceType" NOT NULL DEFAULT 'repair',
    "status" "ServiceStatus" NOT NULL DEFAULT 'requested',
    "description" TEXT NOT NULL,
    "diagnosisNotes" TEXT,
    "preferredDate" TIMESTAMP(3),
    "scheduledDate" TIMESTAMP(3),
    "completedDate" TIMESTAMP(3),
    "estimatedDuration" INTEGER,
    "estimatedCost" DECIMAL(15,2),
    "finalCost" DECIMAL(15,2),
    "isPaid" BOOLEAN NOT NULL DEFAULT false,
    "paidAt" TIMESTAMP(3),
    "createdBy" TEXT NOT NULL,
    "updatedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ServiceRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceItem" (
    "id" TEXT NOT NULL,
    "serviceRequestId" TEXT NOT NULL,
    "productId" TEXT,
    "description" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "unitPrice" DECIMAL(15,2),
    "totalPrice" DECIMAL(15,2),
    "itemType" "ServiceItemType" NOT NULL DEFAULT 'part',
    "laborHours" DECIMAL(5,2),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ServiceItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceStatusHistory" (
    "id" TEXT NOT NULL,
    "serviceRequestId" TEXT NOT NULL,
    "previousStatus" "ServiceStatus",
    "newStatus" "ServiceStatus" NOT NULL,
    "notes" TEXT,
    "changedBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ServiceStatusHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CategorySection" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "image" TEXT NOT NULL,
    "href" TEXT NOT NULL,

    CONSTRAINT "CategorySection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_UserToGroup" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_UserToGroup_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_OrderToServiceRequest" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_OrderToServiceRequest_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_userAM_key" ON "User"("userAM");

-- CreateIndex
CREATE UNIQUE INDEX "User_externalId_key" ON "User"("externalId");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "User_lastName_firstName_idx" ON "User"("lastName", "firstName");

-- CreateIndex
CREATE INDEX "User_phoneNumber_idx" ON "User"("phoneNumber");

-- CreateIndex
CREATE INDEX "User_lastLoginAt_idx" ON "User"("lastLoginAt");

-- CreateIndex
CREATE INDEX "User_isActive_deletedAt_idx" ON "User"("isActive", "deletedAt");

-- CreateIndex
CREATE INDEX "User_externalId_idx" ON "User"("externalId");

-- CreateIndex
CREATE INDEX "User_role_idx" ON "User"("role");

-- CreateIndex
CREATE INDEX "User_lastActivityAt_idx" ON "User"("lastActivityAt");

-- CreateIndex
CREATE INDEX "User_isSuspended_idx" ON "User"("isSuspended");

-- CreateIndex
CREATE UNIQUE INDEX "UserSession_sessionToken_key" ON "UserSession"("sessionToken");

-- CreateIndex
CREATE INDEX "UserSession_sessionToken_idx" ON "UserSession"("sessionToken");

-- CreateIndex
CREATE INDEX "UserSession_userId_expiresAt_idx" ON "UserSession"("userId", "expiresAt");

-- CreateIndex
CREATE INDEX "UserSession_isRevoked_idx" ON "UserSession"("isRevoked");

-- CreateIndex
CREATE UNIQUE INDEX "UserGroup_name_key" ON "UserGroup"("name");

-- CreateIndex
CREATE INDEX "UserSecurityQuestion_userId_idx" ON "UserSecurityQuestion"("userId");

-- CreateIndex
CREATE INDEX "UserNotification_userId_isRead_idx" ON "UserNotification"("userId", "isRead");

-- CreateIndex
CREATE INDEX "UserNotification_userId_createdAt_idx" ON "UserNotification"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "UserAuditLog_userId_idx" ON "UserAuditLog"("userId");

-- CreateIndex
CREATE INDEX "UserAuditLog_action_idx" ON "UserAuditLog"("action");

-- CreateIndex
CREATE INDEX "UserAuditLog_entityType_entityId_idx" ON "UserAuditLog"("entityType", "entityId");

-- CreateIndex
CREATE INDEX "UserAuditLog_createdAt_idx" ON "UserAuditLog"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "Order_orderNumber_key" ON "Order"("orderNumber");

-- CreateIndex
CREATE INDEX "Order_orderNumber_idx" ON "Order"("orderNumber");

-- CreateIndex
CREATE INDEX "Order_userId_idx" ON "Order"("userId");

-- CreateIndex
CREATE INDEX "Order_orderStatus_idx" ON "Order"("orderStatus");

-- CreateIndex
CREATE INDEX "Order_createdAt_idx" ON "Order"("createdAt");

-- CreateIndex
CREATE INDEX "Order_placedAt_orderStatus_idx" ON "Order"("placedAt", "orderStatus");

-- CreateIndex
CREATE INDEX "Order_userId_orderStatus_createdAt_idx" ON "Order"("userId", "orderStatus", "createdAt");

-- CreateIndex
CREATE INDEX "Order_orderStatus_paymentStatus_idx" ON "Order"("orderStatus", "paymentStatus");

-- CreateIndex
CREATE INDEX "Order_isActive_deletedAt_idx" ON "Order"("isActive", "deletedAt");

-- CreateIndex
CREATE INDEX "Order_archivedAt_idx" ON "Order"("archivedAt");

-- CreateIndex
CREATE INDEX "Order_hasReturns_idx" ON "Order"("hasReturns");

-- CreateIndex
CREATE INDEX "Order_hasServiceRequests_idx" ON "Order"("hasServiceRequests");

-- CreateIndex
CREATE INDEX "OrderItem_orderId_idx" ON "OrderItem"("orderId");

-- CreateIndex
CREATE INDEX "OrderItem_productId_idx" ON "OrderItem"("productId");

-- CreateIndex
CREATE INDEX "OrderItem_orderId_createdAt_idx" ON "OrderItem"("orderId", "createdAt");

-- CreateIndex
CREATE INDEX "OrderItem_updatedBy_idx" ON "OrderItem"("updatedBy");

-- CreateIndex
CREATE INDEX "OrderItem_version_idx" ON "OrderItem"("version");

-- CreateIndex
CREATE UNIQUE INDEX "OrderItem_orderId_productId_key" ON "OrderItem"("orderId", "productId");

-- CreateIndex
CREATE INDEX "Wishlist_userId_idx" ON "Wishlist"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Wishlist_userId_productCode_key" ON "Wishlist"("userId", "productCode");

-- CreateIndex
CREATE INDEX "ShippingAddress_phoneNumber_idx" ON "ShippingAddress"("phoneNumber");

-- CreateIndex
CREATE INDEX "BillingAddress_cui_idx" ON "BillingAddress"("cui");

-- CreateIndex
CREATE INDEX "BillingAddress_iban_idx" ON "BillingAddress"("iban");

-- CreateIndex
CREATE INDEX "Banner_placement_position_isActive_idx" ON "Banner"("placement", "position", "isActive");

-- CreateIndex
CREATE INDEX "Banner_startDate_endDate_idx" ON "Banner"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "Banner_deviceTarget_idx" ON "Banner"("deviceTarget");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel1_name_key" ON "CategoryLevel1"("name");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel2_name_level1Id_key" ON "CategoryLevel2"("name", "level1Id");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel3_familyCode_key" ON "CategoryLevel3"("familyCode");

-- CreateIndex
CREATE UNIQUE INDEX "CategoryLevel3_slug_key" ON "CategoryLevel3"("slug");

-- CreateIndex
CREATE INDEX "CategoryLevel3_familyCode_idx" ON "CategoryLevel3"("familyCode");

-- CreateIndex
CREATE INDEX "CategoryLevel3_slug_idx" ON "CategoryLevel3"("slug");

-- CreateIndex
CREATE INDEX "CategoryLevel3_displayOrder_idx" ON "CategoryLevel3"("displayOrder");

-- CreateIndex
CREATE INDEX "CategoryLevel3_productCount_idx" ON "CategoryLevel3"("productCount");

-- CreateIndex
CREATE INDEX "CategoryLevel3_isActive_deletedAt_idx" ON "CategoryLevel3"("isActive", "deletedAt");

-- CreateIndex
CREATE UNIQUE INDEX "Brand_name_key" ON "Brand"("name");

-- CreateIndex
CREATE UNIQUE INDEX "VehicleModel_name_key" ON "VehicleModel"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ProductClass_classCode_key" ON "ProductClass"("classCode");

-- CreateIndex
CREATE INDEX "ProductClass_classCode_idx" ON "ProductClass"("classCode");

-- CreateIndex
CREATE UNIQUE INDEX "Product_Material_Number_key" ON "Product"("Material_Number");

-- CreateIndex
CREATE INDEX "Product_HasDiscount_FinalPrice_idx" ON "Product"("HasDiscount", "FinalPrice");

-- CreateIndex
CREATE INDEX "Product_categoryLevel3Id_HasDiscount_idx" ON "Product"("categoryLevel3Id", "HasDiscount");

-- CreateIndex
CREATE INDEX "Product_categoryLevel3Id_FinalPrice_idx" ON "Product"("categoryLevel3Id", "FinalPrice");

-- CreateIndex
CREATE INDEX "Product_priceRange_HasDiscount_idx" ON "Product"("priceRange", "HasDiscount");

-- CreateIndex
CREATE INDEX "Product_discountPercentage_idx" ON "Product"("discountPercentage");

-- CreateIndex
CREATE INDEX "Product_Material_Number_idx" ON "Product"("Material_Number");

-- CreateIndex
CREATE INDEX "Product_Material_Group_idx" ON "Product"("Material_Group");

-- CreateIndex
CREATE INDEX "Product_Parts_Class_idx" ON "Product"("Parts_Class");

-- CreateIndex
CREATE INDEX "Product_Material_Group_Parts_Class_idx" ON "Product"("Material_Group", "Parts_Class");

-- CreateIndex
CREATE INDEX "Product_categoryLevel3Id_Material_Group_idx" ON "Product"("categoryLevel3Id", "Material_Group");

-- CreateIndex
CREATE INDEX "Product_PretAM_HasDiscount_idx" ON "Product"("PretAM", "HasDiscount");

-- CreateIndex
CREATE INDEX "Product_IsOnLandingPage_categoryLevel3Id_idx" ON "Product"("IsOnLandingPage", "categoryLevel3Id");

-- CreateIndex
CREATE INDEX "Product_createdAt_categoryLevel3Id_idx" ON "Product"("createdAt", "categoryLevel3Id");

-- CreateIndex
CREATE INDEX "Product_Material_Number_normalized_idx" ON "Product"("Material_Number_normalized");

-- CreateIndex
CREATE INDEX "Product_Description_Local_normalized_idx" ON "Product"("Description_Local_normalized");

-- CreateIndex
CREATE INDEX "Product_stockStatus_idx" ON "Product"("stockStatus");

-- CreateIndex
CREATE INDEX "Product_isActive_deletedAt_idx" ON "Product"("isActive", "deletedAt");

-- CreateIndex
CREATE INDEX "Product_updatedBy_idx" ON "Product"("updatedBy");

-- CreateIndex
CREATE INDEX "Product_version_idx" ON "Product"("version");

-- CreateIndex
CREATE INDEX "PriceHistory_productId_idx" ON "PriceHistory"("productId");

-- CreateIndex
CREATE INDEX "PriceHistory_productId_createdAt_idx" ON "PriceHistory"("productId", "createdAt");

-- CreateIndex
CREATE INDEX "PriceHistory_createdAt_idx" ON "PriceHistory"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "Discount_name_key" ON "Discount"("name");

-- CreateIndex
CREATE INDEX "Discount_active_idx" ON "Discount"("active");

-- CreateIndex
CREATE INDEX "Discount_name_idx" ON "Discount"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ProductDiscount_productId_key" ON "ProductDiscount"("productId");

-- CreateIndex
CREATE INDEX "ProductDiscount_discountId_idx" ON "ProductDiscount"("discountId");

-- CreateIndex
CREATE INDEX "ProductAttribute_key_value_idx" ON "ProductAttribute"("key", "value");

-- CreateIndex
CREATE INDEX "ProductAttribute_Material_Number_idx" ON "ProductAttribute"("Material_Number");

-- CreateIndex
CREATE UNIQUE INDEX "ProductAttribute_Material_Number_key_key" ON "ProductAttribute"("Material_Number", "key");

-- CreateIndex
CREATE INDEX "ProductAttributeHistory_Material_Number_idx" ON "ProductAttributeHistory"("Material_Number");

-- CreateIndex
CREATE INDEX "ProductView_productId_viewedAt_idx" ON "ProductView"("productId", "viewedAt");

-- CreateIndex
CREATE INDEX "ProductView_userId_viewedAt_idx" ON "ProductView"("userId", "viewedAt");

-- CreateIndex
CREATE INDEX "ProductView_viewedAt_idx" ON "ProductView"("viewedAt");

-- CreateIndex
CREATE UNIQUE INDEX "SystemConfig_key_key" ON "SystemConfig"("key");

-- CreateIndex
CREATE INDEX "SystemConfig_key_isActive_idx" ON "SystemConfig"("key", "isActive");

-- CreateIndex
CREATE INDEX "ArchivedOrder_archivedAt_idx" ON "ArchivedOrder"("archivedAt");

-- CreateIndex
CREATE INDEX "ArchivedOrder_originalId_idx" ON "ArchivedOrder"("originalId");

-- CreateIndex
CREATE INDEX "ArchivedOrder_userId_archivedAt_idx" ON "ArchivedOrder"("userId", "archivedAt");

-- CreateIndex
CREATE INDEX "ArchivedOrder_originalCreatedAt_idx" ON "ArchivedOrder"("originalCreatedAt");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_orderId_idx" ON "OrderStatusHistory"("orderId");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_orderId_createdAt_idx" ON "OrderStatusHistory"("orderId", "createdAt");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_orderStatus_idx" ON "OrderStatusHistory"("orderStatus");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_shipmentStatus_idx" ON "OrderStatusHistory"("shipmentStatus");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_paymentStatus_idx" ON "OrderStatusHistory"("paymentStatus");

-- CreateIndex
CREATE INDEX "OrderStatusHistory_changedBy_idx" ON "OrderStatusHistory"("changedBy");

-- CreateIndex
CREATE UNIQUE INDEX "Return_returnNumber_key" ON "Return"("returnNumber");

-- CreateIndex
CREATE INDEX "Return_orderId_idx" ON "Return"("orderId");

-- CreateIndex
CREATE INDEX "Return_status_idx" ON "Return"("status");

-- CreateIndex
CREATE INDEX "Return_createdAt_idx" ON "Return"("createdAt");

-- CreateIndex
CREATE INDEX "Return_returnNumber_idx" ON "Return"("returnNumber");

-- CreateIndex
CREATE INDEX "ReturnItem_returnId_idx" ON "ReturnItem"("returnId");

-- CreateIndex
CREATE INDEX "ReturnItem_orderItemId_idx" ON "ReturnItem"("orderItemId");

-- CreateIndex
CREATE UNIQUE INDEX "ReturnItem_returnId_orderItemId_key" ON "ReturnItem"("returnId", "orderItemId");

-- CreateIndex
CREATE INDEX "ReturnStatusHistory_returnId_idx" ON "ReturnStatusHistory"("returnId");

-- CreateIndex
CREATE INDEX "ReturnStatusHistory_returnId_createdAt_idx" ON "ReturnStatusHistory"("returnId", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "ServiceRequest_serviceNumber_key" ON "ServiceRequest"("serviceNumber");

-- CreateIndex
CREATE INDEX "ServiceRequest_userId_idx" ON "ServiceRequest"("userId");

-- CreateIndex
CREATE INDEX "ServiceRequest_status_idx" ON "ServiceRequest"("status");

-- CreateIndex
CREATE INDEX "ServiceRequest_serviceNumber_idx" ON "ServiceRequest"("serviceNumber");

-- CreateIndex
CREATE INDEX "ServiceRequest_scheduledDate_idx" ON "ServiceRequest"("scheduledDate");

-- CreateIndex
CREATE INDEX "ServiceRequest_vin_idx" ON "ServiceRequest"("vin");

-- CreateIndex
CREATE INDEX "ServiceItem_serviceRequestId_idx" ON "ServiceItem"("serviceRequestId");

-- CreateIndex
CREATE INDEX "ServiceItem_productId_idx" ON "ServiceItem"("productId");

-- CreateIndex
CREATE INDEX "ServiceStatusHistory_serviceRequestId_idx" ON "ServiceStatusHistory"("serviceRequestId");

-- CreateIndex
CREATE INDEX "ServiceStatusHistory_serviceRequestId_createdAt_idx" ON "ServiceStatusHistory"("serviceRequestId", "createdAt");

-- CreateIndex
CREATE INDEX "_UserToGroup_B_index" ON "_UserToGroup"("B");

-- CreateIndex
CREATE INDEX "_OrderToServiceRequest_B_index" ON "_OrderToServiceRequest"("B");

-- AddForeignKey
ALTER TABLE "UserSession" ADD CONSTRAINT "UserSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserSecurityQuestion" ADD CONSTRAINT "UserSecurityQuestion_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserNotification" ADD CONSTRAINT "UserNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAuditLog" ADD CONSTRAINT "UserAuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_billingAddressId_fkey" FOREIGN KEY ("billingAddressId") REFERENCES "BillingAddress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_shippingAddressId_fkey" FOREIGN KEY ("shippingAddressId") REFERENCES "ShippingAddress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Wishlist" ADD CONSTRAINT "Wishlist_productCode_fkey" FOREIGN KEY ("productCode") REFERENCES "Product"("Material_Number") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Wishlist" ADD CONSTRAINT "Wishlist_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ShippingAddress" ADD CONSTRAINT "ShippingAddress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BillingAddress" ADD CONSTRAINT "BillingAddress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CategoryLevel2" ADD CONSTRAINT "CategoryLevel2_level1Id_fkey" FOREIGN KEY ("level1Id") REFERENCES "CategoryLevel1"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CategoryLevel3" ADD CONSTRAINT "CategoryLevel3_level2Id_fkey" FOREIGN KEY ("level2Id") REFERENCES "CategoryLevel2"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductClass" ADD CONSTRAINT "ProductClass_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "Brand"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductClassVehicleModel" ADD CONSTRAINT "ProductClassVehicleModel_productClassId_fkey" FOREIGN KEY ("productClassId") REFERENCES "ProductClass"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductClassVehicleModel" ADD CONSTRAINT "ProductClassVehicleModel_vehicleModelId_fkey" FOREIGN KEY ("vehicleModelId") REFERENCES "VehicleModel"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_classId_fkey" FOREIGN KEY ("classId") REFERENCES "ProductClass"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Product" ADD CONSTRAINT "Product_categoryLevel3Id_fkey" FOREIGN KEY ("categoryLevel3Id") REFERENCES "CategoryLevel3"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PriceHistory" ADD CONSTRAINT "PriceHistory_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductHistory" ADD CONSTRAINT "ProductHistory_Material_Number_fkey" FOREIGN KEY ("Material_Number") REFERENCES "Product"("Material_Number") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductDiscount" ADD CONSTRAINT "ProductDiscount_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductDiscount" ADD CONSTRAINT "ProductDiscount_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscountHistory" ADD CONSTRAINT "DiscountHistory_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "Discount"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttribute" ADD CONSTRAINT "ProductAttribute_Material_Number_fkey" FOREIGN KEY ("Material_Number") REFERENCES "Product"("Material_Number") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttributeHistory" ADD CONSTRAINT "ProductAttributeHistory_Material_Number_fkey" FOREIGN KEY ("Material_Number") REFERENCES "Product"("Material_Number") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductView" ADD CONSTRAINT "ProductView_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductView" ADD CONSTRAINT "ProductView_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderStatusHistory" ADD CONSTRAINT "OrderStatusHistory_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Return" ADD CONSTRAINT "Return_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnItem" ADD CONSTRAINT "ReturnItem_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnItem" ADD CONSTRAINT "ReturnItem_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "OrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ReturnStatusHistory" ADD CONSTRAINT "ReturnStatusHistory_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceItem" ADD CONSTRAINT "ServiceItem_serviceRequestId_fkey" FOREIGN KEY ("serviceRequestId") REFERENCES "ServiceRequest"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceItem" ADD CONSTRAINT "ServiceItem_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceStatusHistory" ADD CONSTRAINT "ServiceStatusHistory_serviceRequestId_fkey" FOREIGN KEY ("serviceRequestId") REFERENCES "ServiceRequest"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserToGroup" ADD CONSTRAINT "_UserToGroup_A_fkey" FOREIGN KEY ("A") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserToGroup" ADD CONSTRAINT "_UserToGroup_B_fkey" FOREIGN KEY ("B") REFERENCES "UserGroup"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_OrderToServiceRequest" ADD CONSTRAINT "_OrderToServiceRequest_A_fkey" FOREIGN KEY ("A") REFERENCES "Order"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_OrderToServiceRequest" ADD CONSTRAINT "_OrderToServiceRequest_B_fkey" FOREIGN KEY ("B") REFERENCES "ServiceRequest"("id") ON DELETE CASCADE ON UPDATE CASCADE;
