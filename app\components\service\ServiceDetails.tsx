import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ServiceStatus, IssueType, ResolutionType } from "@/generated/prisma";
import { formatDate } from "@/app/utils/formatters";
import ServiceStatusUpdate from "./ServiceStatusUpdate";

interface ServiceDetailsProps {
  serviceRequest: {
    id: string;
    serviceNumber: string;
    status: ServiceStatus;
    issueType: IssueType;
    description: string;
    resolution?: ResolutionType | null;
    resolutionNotes?: string | null;
    resolvedAt?: Date | null;
    createdAt: Date;
    updatedAt: Date;
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
    action?: {
      orderItem: {
        order: {
          id: string;
          orderNumber: string;
        };
        product: {
          id: string;
          Material_Number: string;
          Description_Local: string | null;
        };
      };
    } | null;
    address?: {
      id: string;
      fullName: string;
      address: string;
      city: string;
      county: string;
      phoneNumber: string;
    } | null;
    showroom?: {
      id: string;
      name: string;
      address1: string;
      city: string;
      county: string;
      phone: string;
    } | null;
    statusHistory: {
      id: string;
      previousStatus: ServiceStatus | null;
      newStatus: ServiceStatus;
      notes: string | null;
      changedBy: string;
      changedAt: Date;
    }[];
  };
}

export default function ServiceDetails({ serviceRequest }: ServiceDetailsProps) {
  // Helper function to get status badge color
  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ServiceStatus.scheduled:
        return "bg-yellow-100 text-yellow-800";
      case ServiceStatus.inProgress:
        return "bg-orange-100 text-orange-800";
      case ServiceStatus.diagnosisComplete:
        return "bg-purple-100 text-purple-800";
      case ServiceStatus.awaitingParts:
        return "bg-amber-100 text-amber-800";
      case ServiceStatus.awaitingApproval:
        return "bg-indigo-100 text-indigo-800";
      case ServiceStatus.completed:
        return "bg-green-100 text-green-800";
      case ServiceStatus.delivered:
        return "bg-emerald-100 text-emerald-800";
      case ServiceStatus.cancelled:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">Service Request {serviceRequest.serviceNumber}</h1>
          <p className="text-gray-500 mt-1">
            Created on {formatDate(serviceRequest.createdAt)}
          </p>
        </div>
        <div className="flex gap-2">
          <Badge className={getStatusColor(serviceRequest.status)} variant="secondary">
            {serviceRequest.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </Badge>
          <Button asChild variant="outline">
            <Link href="/services">← Back to Services</Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Service Information */}
          <Card>
            <CardHeader>
              <CardTitle>Service Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Issue Type</label>
                  <p className="mt-1">
                    {serviceRequest.issueType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">
                    <Badge className={getStatusColor(serviceRequest.status)}>
                      {serviceRequest.status.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Badge>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Description</label>
                <p className="mt-1 text-sm bg-gray-50 p-3 rounded-md">
                  {serviceRequest.description}
                </p>
              </div>

              {serviceRequest.resolution && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Resolution</label>
                  <p className="mt-1">
                    {serviceRequest.resolution.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </p>
                  {serviceRequest.resolutionNotes && (
                    <p className="mt-1 text-sm bg-gray-50 p-3 rounded-md">
                      {serviceRequest.resolutionNotes}
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Related Product/Order */}
          {serviceRequest.action && (
            <Card>
              <CardHeader>
                <CardTitle>Related Order & Product</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Order</label>
                    <p className="mt-1">
                      <Link 
                        href={`/orders/${serviceRequest.action.orderItem.order.id}`}
                        className="text-blue-600 hover:underline"
                      >
                        {serviceRequest.action.orderItem.order.orderNumber}
                      </Link>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Product</label>
                    <p className="mt-1">
                      <span className="font-medium">
                        {serviceRequest.action.orderItem.product.Material_Number}
                      </span>
                      {serviceRequest.action.orderItem.product.Description_Local && (
                        <span className="text-gray-600 ml-2">
                          - {serviceRequest.action.orderItem.product.Description_Local}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Status History */}
          <Card>
            <CardHeader>
              <CardTitle>Status History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {serviceRequest.statusHistory.map((history, index) => (
                  <div key={history.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(history.newStatus)} variant="outline">
                          {history.newStatus.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {formatDate(history.changedAt)}
                        </span>
                      </div>
                      {history.notes && (
                        <p className="text-sm text-gray-600 mt-1">{history.notes}</p>
                      )}
                      <p className="text-xs text-gray-400 mt-1">
                        Changed by: {history.changedBy}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Name</label>
                <p className="mt-1">
                  {serviceRequest.user.firstName} {serviceRequest.user.lastName}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="mt-1">{serviceRequest.user.email}</p>
              </div>
            </CardContent>
          </Card>

          {/* Service Address */}
          {serviceRequest.address && (
            <Card>
              <CardHeader>
                <CardTitle>Service Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="font-medium">{serviceRequest.address.fullName}</p>
                <p className="text-sm text-gray-600">{serviceRequest.address.address}</p>
                <p className="text-sm text-gray-600">
                  {serviceRequest.address.city}, {serviceRequest.address.county}
                </p>
                <p className="text-sm text-gray-600">{serviceRequest.address.phoneNumber}</p>
              </CardContent>
            </Card>
          )}

          {/* Showroom Pickup */}
          {serviceRequest.showroom && (
            <Card>
              <CardHeader>
                <CardTitle>Showroom Pickup</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="font-medium">{serviceRequest.showroom.name}</p>
                <p className="text-sm text-gray-600">{serviceRequest.showroom.address1}</p>
                <p className="text-sm text-gray-600">
                  {serviceRequest.showroom.city}, {serviceRequest.showroom.county}
                </p>
                <p className="text-sm text-gray-600">{serviceRequest.showroom.phone}</p>
              </CardContent>
            </Card>
          )}

          {/* Status Update */}
          <ServiceStatusUpdate serviceRequest={serviceRequest} />
        </div>
      </div>
    </div>
  );
}
