/*
  Warnings:

  - You are about to drop the column `completedDate` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `createdBy` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `diagnosisNotes` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `estimatedCost` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `estimatedDuration` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `finalCost` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `isPaid` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `mileage` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `paidAt` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `preferredDate` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `scheduledDate` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `updatedBy` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `vehicleModel` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `vehicleYear` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `vin` on the `ServiceRequest` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `ServiceStatusHistory` table. All the data in the column will be lost.
  - You are about to drop the `ServiceItem` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_OrderToServiceRequest` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `addressId` to the `ServiceRequest` table without a default value. This is not possible if the table is not empty.
  - Added the required column `issueType` to the `ServiceRequest` table without a default value. This is not possible if the table is not empty.
  - Added the required column `orderId` to the `ServiceRequest` table without a default value. This is not possible if the table is not empty.
  - Added the required column `orderItemId` to the `ServiceRequest` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "IssueType" AS ENUM ('DEFECTIVE_PART', 'DAMAGED_IN_SHIPPING', 'WRONG_ITEM_RECEIVED', 'MISSING_PARTS', 'NOT_AS_DESCRIBED', 'COMPATIBILITY_ISSUE', 'OTHER');

-- CreateEnum
CREATE TYPE "ResolutionType" AS ENUM ('REPLACEMENT', 'REFUND', 'STORE_CREDIT', 'REPAIR', 'PARTIAL_REFUND', 'NO_ACTION_NEEDED');

-- DropForeignKey
ALTER TABLE "ServiceItem" DROP CONSTRAINT "ServiceItem_productId_fkey";

-- DropForeignKey
ALTER TABLE "ServiceItem" DROP CONSTRAINT "ServiceItem_serviceRequestId_fkey";

-- DropForeignKey
ALTER TABLE "ServiceStatusHistory" DROP CONSTRAINT "ServiceStatusHistory_serviceRequestId_fkey";

-- DropForeignKey
ALTER TABLE "_OrderToServiceRequest" DROP CONSTRAINT "_OrderToServiceRequest_A_fkey";

-- DropForeignKey
ALTER TABLE "_OrderToServiceRequest" DROP CONSTRAINT "_OrderToServiceRequest_B_fkey";

-- DropIndex
DROP INDEX "ServiceRequest_scheduledDate_idx";

-- DropIndex
DROP INDEX "ServiceRequest_serviceNumber_idx";

-- DropIndex
DROP INDEX "ServiceRequest_vin_idx";

-- DropIndex
DROP INDEX "ServiceStatusHistory_serviceRequestId_createdAt_idx";

-- AlterTable
ALTER TABLE "ServiceRequest" DROP COLUMN "completedDate",
DROP COLUMN "createdBy",
DROP COLUMN "diagnosisNotes",
DROP COLUMN "estimatedCost",
DROP COLUMN "estimatedDuration",
DROP COLUMN "finalCost",
DROP COLUMN "isPaid",
DROP COLUMN "mileage",
DROP COLUMN "paidAt",
DROP COLUMN "preferredDate",
DROP COLUMN "scheduledDate",
DROP COLUMN "type",
DROP COLUMN "updatedBy",
DROP COLUMN "vehicleModel",
DROP COLUMN "vehicleYear",
DROP COLUMN "vin",
ADD COLUMN     "addressId" TEXT NOT NULL,
ADD COLUMN     "emailsSent" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "issueType" "IssueType" NOT NULL,
ADD COLUMN     "lastEmailSentAt" TIMESTAMP(3),
ADD COLUMN     "orderId" TEXT NOT NULL,
ADD COLUMN     "orderItemId" TEXT NOT NULL,
ADD COLUMN     "resolution" "ResolutionType",
ADD COLUMN     "resolutionNotes" TEXT,
ADD COLUMN     "resolvedAt" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "ServiceStatusHistory" DROP COLUMN "createdAt",
ADD COLUMN     "changedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "emailSent" BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE "ServiceItem";

-- DropTable
DROP TABLE "_OrderToServiceRequest";

-- DropEnum
DROP TYPE "ServiceItemType";

-- DropEnum
DROP TYPE "ServiceType";

-- CreateIndex
CREATE INDEX "ServiceRequest_orderId_idx" ON "ServiceRequest"("orderId");

-- CreateIndex
CREATE INDEX "ServiceRequest_createdAt_idx" ON "ServiceRequest"("createdAt");

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "OrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRequest" ADD CONSTRAINT "ServiceRequest_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "ShippingAddress"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceStatusHistory" ADD CONSTRAINT "ServiceStatusHistory_serviceRequestId_fkey" FOREIGN KEY ("serviceRequestId") REFERENCES "ServiceRequest"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
