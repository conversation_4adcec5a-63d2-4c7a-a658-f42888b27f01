import prisma from "@/app/utils/db";
import { ServiceStatus } from "@/generated/prisma";

export async function getServiceRequestById(id: string) {
  try {
    const serviceRequest = await prisma.serviceRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        action: {
          include: {
            orderItem: {
              include: {
                order: {
                  select: {
                    id: true,
                    orderNumber: true,
                    amount: true,
                    totalAmount: true,
                  },
                },
                product: {
                  select: {
                    id: true,
                    Material_Number: true,
                    Description_Local: true,
                    PretAM: true,
                    FinalPrice: true,
                  },
                },
              },
            },
          },
        },
        address: true,
        showroom: true,
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
        },
      },
    });

    if (!serviceRequest) return null;

    // Convert Decimal fields to numbers for client components
    const serializedServiceRequest = {
      ...serviceRequest,
      action: serviceRequest.action ? {
        ...serviceRequest.action,
        orderItem: {
          ...serviceRequest.action.orderItem,
          price: serviceRequest.action.orderItem.price.toNumber(),
          order: {
            ...serviceRequest.action.orderItem.order,
            amount: serviceRequest.action.orderItem.order.amount.toNumber(),
            totalAmount: serviceRequest.action.orderItem.order.totalAmount.toNumber(),
          },
          product: {
            ...serviceRequest.action.orderItem.product,
            PretAM: serviceRequest.action.orderItem.product.PretAM?.toNumber() || null,
            FinalPrice: serviceRequest.action.orderItem.product.FinalPrice?.toNumber() || null,
          },
        },
      } : null,
    };

    return serializedServiceRequest;
  } catch (error) {
    console.error("Error fetching service request:", error);
    return null;
  }
}

export async function getServiceRequests(limit = 50, status?: ServiceStatus) {
  try {
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: status ? { status } : undefined,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        action: {
          include: {
            orderItem: {
              select: {
                price: true,
              },
              include: {
                product: {
                  select: {
                    Description_Local: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Convert Decimal fields to numbers for client components
    const serializedServiceRequests = serviceRequests.map(request => ({
      ...request,
      action: request.action ? {
        ...request.action,
        orderItem: {
          ...request.action.orderItem,
          price: request.action.orderItem.price.toNumber(),
        },
      } : null,
    }));

    return serializedServiceRequests;
  } catch (error) {
    console.error("Error fetching service requests:", error);
    return [];
  }
}

export async function getServiceRequestsByUserId(userId: string) {
  try {
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: { userId },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        action: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
          take: 1,
        },
      },
    });

    return serviceRequests;
  } catch (error) {
    console.error("Error fetching service requests for user:", error);
    return [];
  }
}
