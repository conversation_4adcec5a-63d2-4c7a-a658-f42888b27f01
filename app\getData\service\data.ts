import prisma from "@/app/utils/db";
import { ServiceStatus } from "@/generated/prisma";

export async function getServiceRequestById(id: string) {
  try {
    const serviceRequest = await prisma.serviceRequest.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        action: {
          include: {
            orderItem: {
              include: {
                order: true,
                product: true,
              },
            },
          },
        },
        address: true,
        showroom: true,
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
        },
      },
    });

    return serviceRequest;
  } catch (error) {
    console.error("Error fetching service request:", error);
    return null;
  }
}

export async function getServiceRequests(limit = 50, status?: ServiceStatus) {
  try {
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: status ? { status } : undefined,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        action: {
          include: {
            orderItem: {
              include: {
                product: {
                  select: {
                    Description_Local: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return serviceRequests;
  } catch (error) {
    console.error("Error fetching service requests:", error);
    return [];
  }
}

export async function getServiceRequestsByUserId(userId: string) {
  try {
    const serviceRequests = await prisma.serviceRequest.findMany({
      where: { userId },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        action: {
          include: {
            orderItem: {
              include: {
                product: true,
              },
            },
          },
        },
        statusHistory: {
          orderBy: {
            changedAt: "desc",
          },
          take: 1,
        },
      },
    });

    return serviceRequests;
  } catch (error) {
    console.error("Error fetching service requests for user:", error);
    return [];
  }
}
