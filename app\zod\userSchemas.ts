import { z } from "zod";
import { <PERSON><PERSON><PERSON>, Rol } from "@/generated/prisma";

// Base user schema with common fields
const userBaseSchema = z.object({
  //Profile
  email: z.string().email("Invalid email address"),
  firstName: z.string().min(2, "First name must be at least 2 characters").max(50, "First name cannot exceed 50 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters").max(50, "Last name cannot exceed 50 characters"),
  //profileImage: z.string().url("Invalid profile image URL").optional().or(z.literal("")),
  userAM: z.string().max(20, "User AM cannot exceed 20 characters").optional().or(z.literal("")),
  phoneNumber: z.string().max(20, "Phone number cannot exceed 20 characters").optional().or(z.literal("")),
  
  //Security
  twoFactorEnabled: z.boolean().optional(),
  newpassword:z.string().max(20, "Parola nouă nu poate depăși 20 de caractere").optional(),
  confirmpassword:z.string().max(20, "Confirmarea parolei nu poate depăși 20 de caractere").optional(),

  //Access
  role: z.nativeEnum(Rol),

  // Status
  isActive: z.boolean().default(true),
  isSuspended: z.boolean().default(false),
  suspensionReason: z.string().max(200, "Suspension reason cannot exceed 200 characters").optional().or(z.literal("")),
  inactiveReason: z.string().max(200, "Inactive reason cannot exceed 200 characters").optional().or(z.literal("")),

  // Preferences
  newsletterOptIn: z.boolean().default(false),
  smsNotifications: z.boolean().default(false),
  pushNotifications: z.boolean().default(false),
  emailNotifications: z.boolean().default(false),
})

// Schema for updating an existing user
export const updateUserSchema = userBaseSchema
  .partial() // Make all fields optional for updates
  .extend({
    id: z.string().cuid("Invalid user ID"),
  });

// Export types for use in components and actions
export type UpdateUserFormValues = z.infer<typeof updateUserSchema>;

// Other schemas for user-related operations
export const changeUserPasswordSchema = z.object({
  userId: z.string().cuid("Invalid user ID"),
  newPassword: z.string().min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export const changeUserStatusSchema = z.object({
  userId: z.string().cuid("Invalid user ID"),
  isActive: z.boolean(),
  isSuspended: z.boolean(),
  suspensionReason: z.string().max(500, "Suspension reason cannot exceed 500 characters")
    .optional()
    .or(z.literal(""))
});

export type ChangeUserPasswordFormValues = z.infer<typeof changeUserPasswordSchema>;
export type ChangeUserStatusFormValues = z.infer<typeof changeUserStatusSchema>;

// Add these schemas for user group operations
export const addUserToGroupSchema = z.object({
  userId: z.string().cuid("Invalid user ID"),
  groupId: z.string().cuid("Invalid group ID")
});

export const removeUserFromGroupSchema = z.object({
  userId: z.string().cuid("Invalid user ID"),
  groupId: z.string().cuid("Invalid group ID")
});

// Export types for use in components and actions
export type AddUserToGroupFormValues = z.infer<typeof addUserToGroupSchema>;
export type RemoveUserFromGroupFormValues = z.infer<typeof removeUserFromGroupSchema>;


