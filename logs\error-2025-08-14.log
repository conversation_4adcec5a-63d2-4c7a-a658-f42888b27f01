{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-08-14 15:28:09"}
{"level":"error","message":"getProductDetails -> Error at parsing: [\n  {\n    \"code\": \"too_small\",\n    \"minimum\": 11,\n    \"type\": \"string\",\n    \"inclusive\": true,\n    \"exact\": true,\n    \"message\": \"Material Number must be exactly 11 characters\",\n    \"path\": [\n      \"materialNumber\"\n    ]\n  }\n] by <EMAIL>","timestamp":"2025-08-14 15:28:31"}
