-- Add with a default so existing rows get a value
ALTER TABLE "Order" ADD COLUMN "totalAmount" numeric(12,2) NOT NULL DEFAULT 0;

-- (Optional) if you're also adding shippingCost:
-- ALTER TABLE "Order" ADD COLUMN "shippingCost" numeric(12,2) NOT NULL DEFAULT 0;

-- (Optional) backfill a real total if you have OrderItem lines:
-- UPDATE "Order" o
-- SET "totalAmount" = COALESCE(oi.total, 0) + COALESCE(o."shippingCost", 0)
-- FROM (
--   SELECT "orderId", SUM("quantity" * "unitPrice") AS total
--   FROM "OrderItem"
--   GROUP BY "orderId"
-- ) oi
-- WHERE o.id = oi."orderId";

-- Drop the default so future inserts must set it explicitly (optional)
ALTER TABLE "Order" ALTER COLUMN "totalAmount" DROP DEFAULT;
