  import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";    
import Link from "next/link";

export default function ServiceDashboard() {
    return (
        <Card>  
          <CardHeader>
            <CardTitle>Services</CardTitle>
            <CardDescription>
              Manage customer service requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-500">No recent service requests found.</p>
          </CardContent>
          <CardFooter>
            <Button asChild className="w-full">
              <Link href="/services">View All Service Requests</Link>
            </Button>
          </CardFooter>
        </Card>
    );
}