import { Order, ShippingAddress as PrismaShippingAddress, Showroom } from "@/generated/prisma";

// --- STEP 1: DEFINE THE DATA SHAPES ---

// This defines the shape of a single address object.
// Notice it does NOT include `| null` here.
type AddressObject = {
    id: string;
    notes: string | null;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    isDefault: boolean;
};

// Extended Order type that includes showroom relation
type OrderWithShowroom = Order & {
  showroom?: Showroom | null;
};

// These are the props for our component.
// The `shippingAddress` prop can be an AddressObject OR null.
interface ShippingDisplayProps {
  order: OrderWithShowroom;
  shippingAddress: AddressObject | null;
}

interface LocationDetails {
  name: string;
  address: string;
  phone?: string;
  nota?: string;
}

// This map is great. It should be defined outside the component.
const locationDetailsMap: Record<string, LocationDetails> = {
  'CJ': { name: 'Showroom Cluj', address: 'Str. F<PERSON><PERSON>ii 25, Cluj-Napoca, Romania', phone: "+40751234567", nota: "Program: L-V, 09:00 - 17:00" },
  'BV': { name: 'Showroom Brașov', address: 'Str. Zizinului 3, Brasov, Romania', phone: "+40751234567", nota: "Program: L-V, 09:00 - 17:00" },
  'TM': { name: 'Showroom Timișoara', address: 'Calea Națională 8, Botoșani, Romania', phone: "+40751234567", nota: "Program: L-V, 09:00 - 17:00" },
  'BAN': { name: 'Showroom Băneasa', address: 'Str. George Bacovia 1, București, Romania', phone: "+40751234567", nota: "Program: L-V, 09:00 - 17:00" },
  'OTP': { name: 'Showroom Otopeni', address: 'Calea Bucureștilor 224, Otopeni, Romania', phone: "+40751234567", nota: "Program: L-V, 09:00 - 17:00" },
  'MIL': { name: 'Showroom Militari', address: 'Bd. Iuliu Maniu 7, București, Romania', phone: "+40751234567", nota: "Program: L-V, 09:00 - 17:00" },
  // ... add all your other locations ...
  'UNKNOWN': { name: 'Locație Necunoscută', address: 'Adresă indisponibilă' },
};


// --- STEP 2: THE COMPONENT ITSELF ---

// I've renamed the component to be more descriptive of its function.
export default function ShippingAddress({ order, shippingAddress }: ShippingDisplayProps) {
  const { shippingMethod, showroom } = order;

  switch (shippingMethod) {
    // Case 1: Standard Courier Delivery
    case 'curier':
      // This is a "Guard Clause". If the method is 'curier' but there's no address,
      // we show an error and stop. This is better than showing nothing.
      if (!shippingAddress) {
        return (
            <div className="space-y-2">
                <h4 className="font-medium text-red-600">Eroare</h4>
                <p className="text-sm">Datele de livrare prin curier lipsesc pentru această comandă.</p>
            </div>
        )
      }

      // After the guard clause, TypeScript knows `shippingAddress` is NOT null.
      // So we don't need optional chaining (`?.`) anymore.
      return (
        <div className="space-y-2">
          <h4 className="font-medium">Date livrare curier</h4>
          <div className="rounded-lg p-4 text-sm bg-gray-50">
            <p className="font-medium">{shippingAddress.fullName}</p>
            <p>{shippingAddress.address}</p>
            <p>{shippingAddress.city}, {shippingAddress.county}</p>
            <p className="text-gray-500">Telefon: {shippingAddress.phoneNumber}</p>
            {shippingAddress.notes && (
              <p className="text-gray-500 mt-1">Nota: {shippingAddress.notes}</p>
            )}
          </div>
        </div>
      );

    // Case 2: Showroom Pickup
    case 'showroom':
      // Use actual showroom data from database if available, otherwise fallback to locationDetailsMap
      if (order.showroom) {
        return (
          <div className="space-y-2">
            <h4 className="font-medium">Detalii ridicare personală</h4>
            <div className="rounded-lg p-4 text-sm bg-gray-50">
              <p className="font-medium">{order.showroom.name}</p>
              <p>{order.showroom.address1}</p>
              {order.showroom.address2 && <p>{order.showroom.address2}</p>}
              <p>{order.showroom.city}, {order.showroom.county}</p>
              {order.showroom.postalCode && <p>{order.showroom.postalCode}</p>}
              <p className="text-gray-500">Telefon: {order.showroom.phone}</p>
              {order.showroom.email && <p className="text-gray-500">Email: {order.showroom.email}</p>}
              {order.showroom.program && <p className="text-gray-500">Program: {order.showroom.program}</p>}
              <div className="mt-3 pt-3 border-t">
                <p className="text-xs text-gray-500">Veți fi notificat când comanda este gata de ridicare.</p>
              </div>
            </div>
          </div>
        );
      } else {
        // Fallback to locationDetailsMap if no showroom data in database
        const locationDetails = locationDetailsMap[showroom || 'UNKNOWN'];
        return (
          <div className="space-y-2">
            <h4 className="font-medium">Detalii ridicare personală</h4>
            <div className="rounded-lg p-4 text-sm bg-gray-50">
              <p className="font-medium">{locationDetails.name}</p>
              <p>{locationDetails.address}</p>
              {locationDetails.phone && <p className="text-gray-500">Telefon: {locationDetails.phone}</p>}
              {locationDetails.nota && <p className="text-gray-500">Info: {locationDetails.nota}</p>}
              <div className="mt-3 pt-3 border-t">
                <p className="text-xs text-gray-500">Veți fi notificat când comanda este gata de ridicare.</p>
              </div>
            </div>
          </div>
        );
      }

    // Case 3: Internal Transport
    case 'intern':
      return (
        <div className="space-y-2">
          <h4 className="font-medium">Detalii transport intern</h4>
          <div className="rounded-lg p-4 text-sm bg-gray-50">
            <p>Livrare prin serviciul intern de transport.</p>
          </div>
        </div>
      );

    // Default fallback case
    default:
      return (
        <div className="space-y-2">
          <h4 className="font-medium">Metoda livrare</h4>
          <p>{shippingMethod || 'Nespecificată'}</p>
        </div>
      );
  }
}