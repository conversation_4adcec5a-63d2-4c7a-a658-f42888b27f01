{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:38:28"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:38:28"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:38:28"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:39:13"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:40:41"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:40:41"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:41:26"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:43:23"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:43:23"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-08 09:43:33"}
{"level":"info","message":"Return created: cme2jyz1s0003hxr8y6q6sdw4 for order cmcnf24dk000ahx785s5nbbne by user_30okOLVEvkOIVWiXJdq3IXfrVT1","timestamp":"2025-08-08 11:17:19"}
{"level":"info","message":"Return cme2jyz1s0003hxr8y6q6sdw4 status updated from requested to <NAME_EMAIL>","timestamp":"2025-08-08 11:19:44"}
{"level":"info","message":"Return created: cme2khla0000bhxr8ereasom3 for order cmdre300d0001hxew3jyriu2o by user_30okOLVEvkOIVWiXJdq3IXfrVT1","timestamp":"2025-08-08 11:31:48"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-08-08 12:01:18"}
{"level":"info","message":"User sync <NAME_EMAIL>: 5 users processed, 0 new, 5 updated","timestamp":"2025-08-08 12:03:05"}
{"level":"info","message":"User cmcukwkxz0008hxj4asrqq78p added to group cmdx5t8vt000ghx8c7srgv4<NAME_EMAIL>","timestamp":"2025-08-08 14:23:26"}
