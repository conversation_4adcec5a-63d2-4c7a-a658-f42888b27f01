{"level":"info","message":"Banner created successfully: cmf27r7y80000hxioy8<NAME_EMAIL>","timestamp":"2025-09-02 10:15:04"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:15:17"}
{"level":"info","message":"Discount with name Summer tyres created <NAME_EMAIL>","timestamp":"2025-09-02 10:16:01"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:16:20"}
{"level":"info","message":"DiscountAddProductAction -> Calling updateProductPrice for product cmelwhc3k01ii1ghx263l841y (07119915703)","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Starting updateProductPrice for product cmelwhc3k01ii1ghx263l841y","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Found product 07119915703 with base price 1.05","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Found active discount for 07119915703: PERCENTAGE with value 20","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"PERCENTAGE: 1.05 - 20% = 0.84","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Discount applied: 07119915703 - Original: 1.05, Final: 0.84, Discount %: 20","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Price changed for 07119915703: 1.05 -> 0.84","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"Updated product 07119915703 with new price 0.84","timestamp":"2025-09-02 10:16:26"}
{"level":"error","message":"updateProductPrice error: TypeError: Cannot read properties of undefined (reading 'create')","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"DiscountAddProductAction -> updateProductPrice result: Failed","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"DiscountAddProductAction -> Discount with id cmf27sfge0001hxiouwe8aqdg updated <NAME_EMAIL>","timestamp":"2025-09-02 10:16:26"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:18:08"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:18:15"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:24:26"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:24:53"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:25:08"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:26:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:27:17"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:27:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:27:49"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:28:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:31"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:31"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:32"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:32"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:33"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:34"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:34"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:34"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:35"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:35"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:42"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:43"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:45"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:46"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:30:47"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:32:35"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:32:56"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:33:09"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:33:44"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:33:52"}
{"level":"info","message":"getProductAttributes -> Product \"07119915703\" has the attributes:  by <EMAIL>","timestamp":"2025-09-02 10:38:30"}
{"level":"info","message":"getProductAttributesHistory -> Product \"07119915703\" with <NAME_EMAIL>","timestamp":"2025-09-02 10:38:30"}
{"level":"info","message":"ProductAddAttributeAction -> Attribute \"culoare\" added successfully for product 07119915703 by <EMAIL>","timestamp":"2025-09-02 10:38:37"}
{"level":"info","message":"getProductAttributes -> Product \"07119915703\" has the attributes: culoare -- <NAME_EMAIL>","timestamp":"2025-09-02 10:38:37"}
{"level":"info","message":"getProductAttributesHistory -> Product \"07119915703\" with <NAME_EMAIL>","timestamp":"2025-09-02 10:38:37"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:41:51"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:44:24"}
{"level":"info","message":"getFeaturedProducts -> Featured products <NAME_EMAIL>","timestamp":"2025-09-02 10:44:59"}
{"level":"info","message":"Return cmf0xdah7000bhxt8ibx29s5k status updated from requested to <NAME_EMAIL>","timestamp":"2025-09-02 10:46:51"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-09-02 10:50:09"}
{"level":"info","message":"User cmelwhaxx00001ghx4mmv9sub details retrieved for userEmail","timestamp":"2025-09-02 10:50:53"}
