{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 12:42:34"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 12:44:36"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 12:44:38"}
{"level":"error","message":"getOrderById -> Error at parsing: [\n  {\n    \"validation\": \"cuid\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid cuid\",\n    \"path\": []\n  }\n] by <EMAIL>","timestamp":"2025-08-05 12:44:48"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 12:57:19"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-08-05 12:58:40"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 12:59:08"}
{"level":"error","message":"getCategoriesByFamilyCode -> No family code <NAME_EMAIL>","timestamp":"2025-08-05 12:59:09"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 12:59:58"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 13:59:42"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 14:02:45"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 14:03:24"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 14:03:25"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 14:03:50"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 14:03:56"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 14:05:34"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 15:23:36"}
{"level":"error","message":"[getUserOrders] Error fetching orders :: transformOrderData is not defined\nStack: ReferenceError: transformOrderData is not defined\n    at getOrders (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:269:38)\n    at async OrdersRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:467:20)","timestamp":"2025-08-05 16:17:23"}
{"level":"error","message":"[getUserOrders] Error fetching orders :: transformOrderData is not defined\nStack: ReferenceError: transformOrderData is not defined\n    at getOrders (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:269:38)\n    at async OrdersRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:467:20)","timestamp":"2025-08-05 16:19:37"}
{"level":"error","message":"[getUserOrders] Error fetching orders :: transformOrderData is not defined\nStack: ReferenceError: transformOrderData is not defined\n    at getOrders (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:269:38)\n    at async OrdersRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:467:20)","timestamp":"2025-08-05 16:40:20"}
{"level":"error","message":"[getUserOrders] Error fetching orders :: transformOrderData is not defined\nStack: ReferenceError: transformOrderData is not defined\n    at getOrders (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:269:38)\n    at async OrdersRoute (C:\\Users\\<USER>\\OneDrive - Automobile Bavaria SRL\\Documents\\JS-React\\database-parts\\.next\\server\\chunks\\ssr\\_c21672ad._.js:467:20)","timestamp":"2025-08-05 16:40:28"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:46:10"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:46:31"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:49:58"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:51:10"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:52:37"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:52:37"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:53:21"}
{"level":"error","message":"getOrders -> Error at parsing: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"object\",\n    \"received\": \"undefined\",\n    \"path\": [],\n    \"message\": \"Required\"\n  }\n] by <EMAIL>","timestamp":"2025-08-05 16:54:48"}
