-- Exactly one of (returnId, serviceRequestId) must be set (XOR)
ALTER TABLE "PostPurchaseAction"
ADD CONSTRAINT "ppa_xor_return_service_chk"
CHECK ((("returnId" IS NOT NULL)::int + ("serviceRequestId" IS NOT NULL)::int) = 1);

-- Type must match which link is set
ALTER TABLE "PostPurchaseAction"
ADD CONSTRAINT "ppa_type_matches_link_chk"
CHECK (
  (type = 'RETURN'  AND "returnId" IS NOT NULL AND "serviceRequestId" IS NULL)
  OR
  (type = 'SERVICE' AND "serviceRequestId" IS NOT NULL AND "returnId" IS NULL)
);

-- Only one ACTIVE action per OrderItem (history allowed)
CREATE UNIQUE INDEX IF NOT EXISTS "ppa_one_active_per_item"
ON "PostPurchaseAction" ("orderItemId")
WHERE "status" = 'ACTIVE';
