"use server"


import { revalidatePath } from "next/cache";
import { CreateBannerFormValues, UpdateBannerFormValues, createBannerSchema, updateBannerSchema } from "../zod/zod";
import prisma from "../utils/db";
import { requireAdminOrModerator } from "@/lib/auth-utils";
import { BannerPlacement } from "@/generated/prisma";
import { logError, logInfo } from "@/lib/logger";
import { ReturnAction } from "./actions";

export async function createBanner(data: CreateBannerFormValues) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const validatedData = createBannerSchema.parse(data);
    
    const banner = await prisma.banner.create({
      data: {
        ...validatedData,
        placement: validatedData.placement as BannerPlacement,
        createdBy: userEmail,
        updatedBy: userEmail,
      }
    });
    
    revalidatePath("/banner");
    logInfo(`Banner created successfully: ${banner.id} by ${userEmail}`);
    return { success: true, banner };
  } catch (error) {
    logError(`<PERSON>rror creating banner by ${userEmail}:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create banner" 
    };
  }
}

export async function updateBanner(data: UpdateBannerFormValues) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const validatedData = updateBannerSchema.parse(data);
    const { id, ...updateData } = validatedData;
    
    const banner = await prisma.banner.update({
      where: { id },
      data: {
        ...updateData,
        placement: validatedData.placement as BannerPlacement,
        updatedBy: userEmail,
      }
    });
    
    logInfo(`Banner updated successfully: ${banner.id} by ${userEmail}`);
    revalidatePath("/banner");
    revalidatePath(`/banner/${id}`);
    return { success: true, banner };
  } catch (error) {
    logError(`Error updating banner by ${userEmail}:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to update banner" 
    };
  }
}

export async function deleteBanner(id: string): Promise<ReturnAction> {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    await prisma.banner.delete({
      where: { id },
    });
    
    logInfo(`Banner deleted successfully: ${id} by ${userEmail}`);
    revalidatePath("/banner");
    return {
      status: "SUCCESS",
      message: "Banner deleted successfully",
    };
  } catch (error) {
    logError(`Error deleting banner by ${userEmail}:`, error);
    return {
      status: "ERROR",
      message: "Failed to delete banner",
    };
  }
}

export async function incrementBannerImpressions(id: string) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    await prisma.banner.update({
      where: { id },
      data: {
        impressions: {
          increment: 1
        }
      }
    });
    logInfo(`Banner impression incremented: ${id} by ${userEmail}`);
    return { success: true };
  } catch (error) {
      logError(`Error incrementing banner impressions by ${userEmail}:`, error);
    return { success: false };
  }
}

export async function incrementBannerClicks(id: string) {
  const actor = await requireAdminOrModerator();
  const userEmail = actor.email
  try {
    const banner = await prisma.banner.update({
      where: { id },
      data: {
        clicks: {
          increment: 1
        }
      }
    });
    
    // Update conversion rate
    if (banner.impressions > 0) {
      const conversionRate = (banner.clicks / banner.impressions) * 100;
      await prisma.banner.update({
        where: { id },
        data: {
          conversionRate
        }
      });
    }
    
    logInfo(`Banner click incremented: ${id} by ${userEmail}`);
    return { success: true };
  } catch (error) {
    logError(`Error incrementing banner clicks by ${userEmail}:`, error);
    return { success: false };
  }
}
