"use client";

import { useRouter } from "next/navigation";
import GroupForm from "@/app/components/groups/GroupForm";
import { updateGroup } from "@/app/actions/groupActions";
import { toast } from "sonner";
import { UserGroup } from "@/generated/prisma";

interface EditGroupClientProps {
  group: UserGroup;
}

export default function EditGroupClient({ group }: EditGroupClientProps) {
  const router = useRouter();

  const handleSubmit = async (data: { name: string; description?: string }) => {
    try {
      const result = await updateGroup(group.id, data);
      if (result.status === "SUCCESS") {
        toast.success("Group updated successfully");
        router.push(`/groups/${group.id}`);
      } else {
        toast.error(result.message || "Failed to update group");
      }
    } catch (error) {
      console.error("Error updating group:", error);
      toast.error("Failed to update group");
    }
  };

  return <GroupForm group={group} onSubmit={handleSubmit} />;
}
