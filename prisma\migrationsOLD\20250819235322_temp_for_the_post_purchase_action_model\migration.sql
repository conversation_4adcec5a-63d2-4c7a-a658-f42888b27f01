-- Create<PERSON><PERSON>
CREATE TYPE "ActionType" AS ENUM ('RETURN', 'SERVICE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "ActionStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'CANCELLED');

-- DropForeignKey
ALTER TABLE "ServiceRequest" DROP CONSTRAINT "ServiceRequest_orderId_fkey";

-- DropForeignKey
ALTER TABLE "ServiceRequest" DROP CONSTRAINT "ServiceRequest_orderItemId_fkey";

-- DropIndex
DROP INDEX "Order_hasReturns_idx";

-- DropIndex
DROP INDEX "Order_hasServiceRequests_idx";

-- DropIndex
DROP INDEX "ServiceRequest_orderId_idx";

-- AlterTable
ALTER TABLE "Return" ALTER COLUMN "reason" DROP NOT NULL;

-- AlterTable
ALTER TABLE "ServiceRequest" ALTER COLUMN "orderId" DROP NOT NULL,
ALTER COLUMN "orderItemId" DROP NOT NULL;

-- CreateTable
CREATE TABLE "PostPurchaseAction" (
    "id" TEXT NOT NULL,
    "actionNumber" TEXT NOT NULL,
    "orderItemId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" "ActionType" NOT NULL,
    "status" "ActionStatus" NOT NULL DEFAULT 'ACTIVE',
    "returnId" TEXT,
    "serviceRequestId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PostPurchaseAction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PostPurchaseAction_actionNumber_key" ON "PostPurchaseAction"("actionNumber");

-- CreateIndex
CREATE UNIQUE INDEX "PostPurchaseAction_returnId_key" ON "PostPurchaseAction"("returnId");

-- CreateIndex
CREATE UNIQUE INDEX "PostPurchaseAction_serviceRequestId_key" ON "PostPurchaseAction"("serviceRequestId");

-- CreateIndex
CREATE INDEX "PostPurchaseAction_userId_idx" ON "PostPurchaseAction"("userId");

-- CreateIndex
CREATE INDEX "PostPurchaseAction_orderItemId_idx" ON "PostPurchaseAction"("orderItemId");

-- CreateIndex
CREATE INDEX "PostPurchaseAction_type_idx" ON "PostPurchaseAction"("type");

-- CreateIndex
CREATE INDEX "PostPurchaseAction_status_idx" ON "PostPurchaseAction"("status");

-- CreateIndex
CREATE UNIQUE INDEX "PostPurchaseAction_orderItemId_status_key" ON "PostPurchaseAction"("orderItemId", "status");

-- AddForeignKey
ALTER TABLE "PostPurchaseAction" ADD CONSTRAINT "PostPurchaseAction_orderItemId_fkey" FOREIGN KEY ("orderItemId") REFERENCES "OrderItem"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PostPurchaseAction" ADD CONSTRAINT "PostPurchaseAction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PostPurchaseAction" ADD CONSTRAINT "PostPurchaseAction_returnId_fkey" FOREIGN KEY ("returnId") REFERENCES "Return"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PostPurchaseAction" ADD CONSTRAINT "PostPurchaseAction_serviceRequestId_fkey" FOREIGN KEY ("serviceRequestId") REFERENCES "ServiceRequest"("id") ON DELETE SET NULL ON UPDATE CASCADE;
