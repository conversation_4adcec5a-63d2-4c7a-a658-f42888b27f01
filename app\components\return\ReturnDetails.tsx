import { ReturnStatus } from "@/generated/prisma";
import { formatCurrency, formatDate } from "@/app/utils/formatters";
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface ReturnDetailsProps {
  returnData: any; // Using any for simplicity, but you should define a proper type
}

export default function ReturnDetails({ returnData }: ReturnDetailsProps) {
  // Helper function to get status badge color
  const getStatusColor = (status: ReturnStatus) => {
    switch (status) {
      case ReturnStatus.requested:
        return "bg-blue-100 text-blue-800";
      case ReturnStatus.approved:
        return "bg-green-100 text-green-800";
      case ReturnStatus.received:
        return "bg-purple-100 text-purple-800";
      case ReturnStatus.inspected:
        return "bg-indigo-100 text-indigo-800";
      case ReturnStatus.awaitingReceipt:
        return "bg-yellow-100 text-yellow-800";
      case ReturnStatus.completed:
        return "bg-emerald-100 text-emerald-800";
        case ReturnStatus.refundIssued:
        return "bg-gray-100 text-gray-800";
      case ReturnStatus.rejected:
        return "bg-red-100 text-red-800";
      case ReturnStatus.cancelled:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Return #{returnData.returnNumber}</CardTitle>
          <CardDescription>
            Created on {formatDate(returnData.createdAt)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Status</h3>
              <Badge className={getStatusColor(returnData.status)}>
                {returnData.status.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase())}
              </Badge>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Order</h3>
              <p>{returnData.order?.orderNumber || "N/A"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Customer</h3>
              <p>
                {returnData.order?.user?.firstName} {returnData.order?.user?.lastName}
                <br />
                {returnData.order?.user?.email}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Return Reason</h3>
              {/* <p>{returnData.reason.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase())}</p> */}
              <p>{returnData.reason}</p>
            </div>
          </div>

          {returnData.additionalNotes && (
            <div className="mt-4">
              <h3 className="text-sm font-medium text-gray-500">Additional Notes</h3>
              <p className="mt-1 text-gray-900">{returnData.additionalNotes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Return Items</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>Price</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {returnData.returnItems.map((item: any) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    {item.orderItem.product?.Description_Local}
                  </TableCell>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>
                    {item.reason.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase())}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(item.orderItem.price.toNumber() * item.quantity)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Status History</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Changed By</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {returnData.statusHistory.map((history: any) => (
                <TableRow key={history.id}>
                  <TableCell>{formatDate(history.createdAt)}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(history.newStatus)}>
                      {history.newStatus.replace(/([A-Z])/g, ' $1').replace(/^./, (str: string) => str.toUpperCase())}
                    </Badge>
                  </TableCell>
                  <TableCell>{history.changedBy}</TableCell>
                  <TableCell>{history.notes || "—"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}